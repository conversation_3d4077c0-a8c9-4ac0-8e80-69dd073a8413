/**
 * Calendar Conversion Utilities
 * Utilities for converting between different calendar formats for caching
 */

import { CachedCalendarEvent, CachedCalendarList } from "@/lib/cache/unified/types"

/**
 * Convert Google Calendar event to CachedCalendarEvent format
 */
export function convertGoogleEventToCachedEvent(event: any): CachedCalendarEvent {
  return {
    id: event.id,
    summary: event.summary || '',
    description: event.description,
    location: event.location,
    start: {
      dateTime: event.start?.dateTime,
      date: event.start?.date,
      timeZone: event.start?.timeZone || 'UTC'
    },
    end: {
      dateTime: event.end?.dateTime,
      date: event.end?.date,
      timeZone: event.end?.timeZone || 'UTC'
    },
    attendees: event.attendees?.map((attendee: any) => ({
      email: attendee.email,
      displayName: attendee.displayName,
      responseStatus: attendee.responseStatus || 'needsAction'
    })) || [],
    creator: event.creator ? {
      email: event.creator.email,
      displayName: event.creator.displayName
    } : undefined,
    organizer: event.organizer ? {
      email: event.organizer.email,
      displayName: event.organizer.displayName
    } : undefined,
    recurrence: event.recurrence,
    recurringEventId: event.recurringEventId,
    status: event.status || 'confirmed',
    visibility: event.visibility || 'default',
    created: event.created,
    updated: event.updated,
    htmlLink: event.htmlLink,
    hangoutLink: event.hangoutLink,
    conferenceData: event.conferenceData ? {
      conferenceId: event.conferenceData.conferenceId,
      conferenceSolution: event.conferenceData.conferenceSolution,
      entryPoints: event.conferenceData.entryPoints
    } : undefined,
    reminders: event.reminders || { useDefault: true }
  }
}

/**
 * Convert CachedCalendarEvent back to Google Calendar format
 */
export function convertCachedEventToGoogleEvent(event: CachedCalendarEvent): any {
  return {
    id: event.id,
    summary: event.summary,
    description: event.description,
    location: event.location,
    start: {
      dateTime: event.start.dateTime,
      date: event.start.date,
      timeZone: event.start.timeZone
    },
    end: {
      dateTime: event.end.dateTime,
      date: event.end.date,
      timeZone: event.end.timeZone
    },
    attendees: event.attendees?.map(attendee => ({
      email: attendee.email,
      displayName: attendee.displayName,
      responseStatus: attendee.responseStatus
    })),
    creator: event.creator,
    organizer: event.organizer,
    recurrence: event.recurrence,
    recurringEventId: event.recurringEventId,
    status: event.status,
    visibility: event.visibility,
    created: event.created,
    updated: event.updated,
    htmlLink: event.htmlLink,
    hangoutLink: event.hangoutLink,
    conferenceData: event.conferenceData,
    reminders: event.reminders
  }
}

/**
 * Generate cache key for calendar events
 */
export function generateCalendarEventsCacheKey(
  timeMin?: string,
  timeMax?: string,
  maxResults?: number,
  orderBy?: string,
  calendarId: string = 'primary'
): string {
  return `events_${calendarId}_${timeMin || 'all'}_${timeMax || 'all'}_${maxResults || 'all'}_${orderBy || 'startTime'}`
}

/**
 * Generate cache key for individual calendar event
 */
export function generateCalendarEventCacheKey(eventId: string): string {
  return `event_detail_${eventId}`
}

/**
 * Generate cache key for calendar list
 */
export function generateCalendarListCacheKey(): string {
  return 'calendars_list'
}

/**
 * Generate cache key for free/busy data
 */
export function generateFreeBusyCacheKey(
  timeMin: string,
  timeMax: string,
  calendarIds: string[] = ['primary']
): string {
  return `freebusy_${calendarIds.join(',')}_${timeMin}_${timeMax}`
}

/**
 * Generate cache key for calendar settings
 */
export function generateCalendarSettingsCacheKey(): string {
  return 'calendar_settings'
}

/**
 * Generate cache key for calendar colors
 */
export function generateCalendarColorsCacheKey(): string {
  return 'calendar_colors'
}

/**
 * Convert Google Calendar list to cached format
 */
export function convertGoogleEventsListToCachedList(
  events: any[],
  timeMin?: string,
  timeMax?: string,
  calendarId?: string
): CachedCalendarList {
  return {
    events: events.map(convertGoogleEventToCachedEvent),
    totalCount: events.length,
    timestamp: Date.now(),
    timeMin,
    timeMax,
    calendarId
  }
}

/**
 * Calculate calendar statistics from cached events
 */
export function calculateCalendarStats(events: CachedCalendarEvent[]) {
  const now = new Date()
  const weekFromNow = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000)

  return {
    totalEvents: events.length,
    thisWeekEvents: events.filter(event => {
      const eventDate = new Date(event.start?.dateTime || event.start?.date || '')
      return eventDate >= now && eventDate <= weekFromNow
    }).length,
    upcomingEvents: events.filter(event => {
      const eventDate = new Date(event.start?.dateTime || event.start?.date || '')
      return eventDate >= now
    }).length,
    calendarsCount: 1 // For primary calendar
  }
}

/**
 * Validate calendar event data
 */
export function isValidCalendarEvent(event: any): boolean {
  return !!(
    event &&
    event.id &&
    event.summary &&
    event.start &&
    (event.start.dateTime || event.start.date) &&
    event.end &&
    (event.end.dateTime || event.end.date)
  )
}

/**
 * Clean calendar event data for caching
 */
export function cleanCalendarEventForCache(event: any): any {
  // Remove unnecessary fields that shouldn't be cached
  const {
    etag,
    kind,
    iCalUID,
    sequence,
    ...cleanEvent
  } = event

  return cleanEvent
}
