/**
 * React Hook for App Preloading
 * Provides easy integration with the app preloader system
 */

import { useState, useEffect, useCallback, useRef } from 'react'
import { useSession } from 'next-auth/react'
import { appPreloader, PreloadOptions, PreloadSummary } from '@/lib/preloader/appPreloader'
import { useUnifiedCache } from '@/contexts/cache/UnifiedCacheContext'

export interface UseAppPreloaderOptions {
  autoPreload?: boolean
  priority?: 'high' | 'medium' | 'low'
  modules?: {
    gmail?: boolean
    calendar?: boolean
    meet?: boolean
    aiAgent?: boolean
  }
  timeRanges?: {
    calendar?: {
      pastDays?: number
      futureDays?: number
    }
    gmail?: {
      maxEmails?: number
      includeDrafts?: boolean
      includeSent?: boolean
    }
  }
  onPreloadComplete?: (summary: PreloadSummary) => void
  onPreloadError?: (error: Error) => void
}

export interface UseAppPreloaderReturn {
  // State
  isPreloading: boolean
  preloadSummary: PreloadSummary | null
  error: Error | null
  
  // Actions
  startPreload: () => Promise<PreloadSummary | null>
  cancelPreload: () => void
  
  // Status
  progress: {
    inProgress: boolean
    queueSize: number
  }
  
  // Cache utilities
  getCachedData: <T>(module: 'gmail' | 'calendar' | 'meet' | 'aiAgent', key: string) => T | null
  invalidateCache: (module: 'gmail' | 'calendar' | 'meet' | 'aiAgent', pattern?: string) => number
}

export function useAppPreloader(options: UseAppPreloaderOptions = {}): UseAppPreloaderReturn {
  const { data: session } = useSession()
  const { getCachedData, invalidateCache } = useUnifiedCache()
  
  const [isPreloading, setIsPreloading] = useState(false)
  const [preloadSummary, setPreloadSummary] = useState<PreloadSummary | null>(null)
  const [error, setError] = useState<Error | null>(null)
  const [progress, setProgress] = useState({ inProgress: false, queueSize: 0 })
  
  const abortControllerRef = useRef<AbortController | null>(null)
  const preloadOptionsRef = useRef<PreloadOptions | null>(null)

  const {
    autoPreload = true,
    priority = 'high',
    modules = {
      gmail: true,
      calendar: true,
      meet: true,
      aiAgent: true
    },
    timeRanges = {
      calendar: {
        pastDays: 7,
        futureDays: 30
      },
      gmail: {
        maxEmails: 50,
        includeDrafts: true,
        includeSent: true
      }
    },
    onPreloadComplete,
    onPreloadError
  } = options

  // Update progress periodically
  useEffect(() => {
    if (!session?.user?.id) return

    const interval = setInterval(() => {
      const currentProgress = appPreloader.getPreloadingProgress(session.user.id)
      setProgress(currentProgress)
      setIsPreloading(currentProgress.inProgress)
    }, 500)

    return () => clearInterval(interval)
  }, [session?.user?.id])

  // Prepare preload options
  useEffect(() => {
    if (!session?.user?.id) return

    preloadOptionsRef.current = {
      userId: session.user.id,
      priority,
      modules,
      timeRanges
    }
  }, [session?.user?.id, priority, modules, timeRanges])

  // Start preload function
  const startPreload = useCallback(async (): Promise<PreloadSummary | null> => {
    if (!preloadOptionsRef.current) {
      const error = new Error('No user session available for preloading')
      setError(error)
      onPreloadError?.(error)
      return null
    }

    try {
      setError(null)
      setIsPreloading(true)
      
      // Create abort controller for cancellation
      abortControllerRef.current = new AbortController()
      
      console.log('🚀 Starting app preload with options:', preloadOptionsRef.current)
      
      const summary = await appPreloader.preloadAllAppData(preloadOptionsRef.current)
      
      setPreloadSummary(summary)
      setIsPreloading(false)
      
      console.log('✅ App preload completed:', summary)
      onPreloadComplete?.(summary)
      
      return summary
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Unknown preload error')
      setError(error)
      setIsPreloading(false)
      
      console.error('❌ App preload failed:', error)
      onPreloadError?.(error)
      
      return null
    }
  }, [onPreloadComplete, onPreloadError])

  // Cancel preload function
  const cancelPreload = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort()
      abortControllerRef.current = null
    }
    setIsPreloading(false)
    console.log('🛑 App preload cancelled')
  }, [])

  // Auto preload on session ready
  useEffect(() => {
    if (autoPreload && session?.user?.id && preloadOptionsRef.current && !isPreloading) {
      // Small delay to ensure app is ready
      const timer = setTimeout(() => {
        startPreload()
      }, 1000)
      
      return () => clearTimeout(timer)
    }
  }, [autoPreload, session?.user?.id, startPreload, isPreloading])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort()
      }
    }
  }, [])

  return {
    // State
    isPreloading,
    preloadSummary,
    error,
    
    // Actions
    startPreload,
    cancelPreload,
    
    // Status
    progress,
    
    // Cache utilities
    getCachedData,
    invalidateCache
  }
}

// Additional hook for preload status monitoring
export function usePreloadStatus(userId?: string) {
  const [status, setStatus] = useState({ inProgress: false, queueSize: 0 })

  useEffect(() => {
    if (!userId) return

    const interval = setInterval(() => {
      const currentStatus = appPreloader.getPreloadingProgress(userId)
      setStatus(currentStatus)
    }, 1000)

    return () => clearInterval(interval)
  }, [userId])

  return status
}

// Hook for manual cache warming
export function useCacheWarmer() {
  const { getCachedData, setCachedData } = useUnifiedCache()
  const { data: session } = useSession()

  const warmCache = useCallback(async (
    module: 'gmail' | 'calendar' | 'meet' | 'aiAgent',
    dataType: string,
    fetchFunction: () => Promise<any>
  ) => {
    if (!session?.user?.id) return null

    // Check if data is already cached
    const cached = getCachedData(module, dataType)
    if (cached) {
      console.log(`Cache hit for ${module}:${dataType}`)
      return cached
    }

    // Fetch and cache fresh data
    try {
      const freshData = await fetchFunction()
      if (freshData) {
        setCachedData(module, dataType, freshData)
        console.log(`Cache warmed for ${module}:${dataType}`)
      }
      return freshData
    } catch (error) {
      console.error(`Cache warming failed for ${module}:${dataType}:`, error)
      return null
    }
  }, [getCachedData, setCachedData, session?.user?.id])

  return { warmCache }
}

// Hook for preload performance monitoring
export function usePreloadMetrics() {
  const [metrics, setMetrics] = useState<{
    totalPreloads: number
    averageLoadTime: number
    cacheHitRate: number
    errorRate: number
  }>({
    totalPreloads: 0,
    averageLoadTime: 0,
    cacheHitRate: 0,
    errorRate: 0
  })

  const updateMetrics = useCallback((summary: PreloadSummary) => {
    setMetrics(prev => ({
      totalPreloads: prev.totalPreloads + 1,
      averageLoadTime: (prev.averageLoadTime + summary.totalTime) / 2,
      cacheHitRate: (prev.cacheHitRate + summary.cacheHitRate) / 2,
      errorRate: (prev.errorRate + (summary.errorCount / (summary.successCount + summary.errorCount))) / 2
    }))
  }, [])

  return { metrics, updateMetrics }
}
