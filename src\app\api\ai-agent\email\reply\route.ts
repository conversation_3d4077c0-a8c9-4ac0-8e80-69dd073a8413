import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from "next-auth/next"
import { authOptions } from '@/lib/auth'
import { aiAgentEmailService } from '@/lib/ai/aiAgentEmail'

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions) as any
    
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const {
      emailId,
      context,
      tone = 'professional',
      replyType = 'reply',
      keyPoints,
      sendImmediately = false
    } = await request.json()

    if (!emailId) {
      return NextResponse.json({ 
        error: 'Email ID is required' 
      }, { status: 400 })
    }

    // Generate reply content
    const replyContent = await aiAgentEmailService.generateReply(session.user.id, {
      emailId,
      context,
      tone,
      replyType,
      keyPoints
    })

    // If sendImmediately is true, send the reply
    if (sendImmediately) {
      const sendResult = await aiAgentEmailService.sendReply(
        session.user.id,
        emailId,
        replyContent
      )

      return NextResponse.json({
        replyContent,
        sendResult,
        success: true
      })
    }

    // Otherwise, just return the composed reply
    return NextResponse.json({
      replyContent,
      success: true
    })

  } catch (error) {
    console.error('Email reply error:', error)
    return NextResponse.json(
      { error: 'Failed to generate reply' },
      { status: 500 }
    )
  }
}
