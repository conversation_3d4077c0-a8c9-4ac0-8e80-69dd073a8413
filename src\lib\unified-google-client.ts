import { google } from 'googleapis'
import { prisma } from './prisma'

export type GoogleService = 'gmail' | 'calendar' | 'meet'

export interface GoogleClient {
  gmail?: any
  calendar?: any
  meet?: any
  userEmail: string
  auth: any
}

export interface ConnectionTestResult {
  connected: boolean
  error?: string
}

/**
 * Unified Google API client for Gmail service
 * Simplified to focus on Gmail API only - can be extended later
 */
class UnifiedGoogleClientService {
  
  /**
   * Create OAuth2 client with user's credentials
   */
  private async createAuthClient(userId: string, service: GoogleService) {
    // Get user's OAuth tokens from database
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        gmailRefreshToken: true,
        gmailConnected: true,
        email: true
      }
    })

    if (!user) {
      throw new Error('User not found')
    }

    // For now, use Gmail token for all services until Prisma is regenerated
    // This is a temporary workaround since calendar/meet fields aren't available yet
    let refreshToken: string | null = user.gmailRefreshToken
    let isConnected = user.gmailConnected

    if (!isConnected || !refreshToken) {
      throw new Error(`Google services not connected for this user. Please connect your Google account.`)
    }

    // Create OAuth2 client
    const oauth2Client = new google.auth.OAuth2(
      process.env.GOOGLE_CLIENT_ID,
      process.env.GOOGLE_CLIENT_SECRET,
      process.env.NEXTAUTH_URL + '/api/auth/callback/google'
    )

    // Set the refresh token
    oauth2Client.setCredentials({
      refresh_token: refreshToken
    })

    return { auth: oauth2Client, userEmail: user.email }
  }

  /**
   * Handle authentication errors and update user connection status
   */
  private async handleAuthError(error: Error, userId: string, service: GoogleService) {
    console.error(`Error creating ${service} client:`, error)

    // If the error is about insufficient scopes, mark user as needing reconnection
    if (error.message.includes('insufficient authentication scopes')) {
      // For now, we only have Gmail tokens, so we update Gmail connection
      // When calendar/meet fields are available, we can update them separately
      await prisma.user.update({
        where: { id: userId },
        data: {
          gmailConnected: false,
          gmailRefreshToken: null
        }
      })

      throw new Error(`${service} permissions need to be updated. Please reconnect your Google account.`)
    }

    throw error
  }

  /**
   * Get Gmail client for a user
   */
  async getGmailClient(userId: string): Promise<{ gmail: any; userEmail: string; auth: any }> {
    try {
      const { auth, userEmail } = await this.createAuthClient(userId, 'gmail')

      // Create Gmail client
      const gmail = google.gmail({ version: 'v1', auth })

      return { gmail, userEmail, auth }
    } catch (error) {
      await this.handleAuthError(error as Error, userId, 'gmail')
      throw error // Ensure we always throw after handling error
    }
  }

  /**
   * Get Calendar client for a user
   */
  async getCalendarClient(userId: string): Promise<{ calendar: any; userEmail: string; auth: any }> {
    try {
      const { auth, userEmail } = await this.createAuthClient(userId, 'calendar')

      // Create Calendar client
      const calendar = google.calendar({ version: 'v3', auth })

      return { calendar, userEmail, auth }
    } catch (error) {
      await this.handleAuthError(error as Error, userId, 'calendar')
      throw error // Ensure we always throw after handling error
    }
  }

  /**
   * Get unified client with Gmail and Calendar services
   */
  async getUnifiedClient(userId: string): Promise<GoogleClient> {
    try {
      // Get user data to check connections
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: {
          gmailConnected: true,
          gmailRefreshToken: true,
          email: true
        }
      })

      if (!user) {
        throw new Error('User not found')
      }

      if (!user.gmailRefreshToken) {
        throw new Error('No Google services connected for this user')
      }

      const oauth2Client = new google.auth.OAuth2(
        process.env.GOOGLE_CLIENT_ID,
        process.env.GOOGLE_CLIENT_SECRET,
        process.env.NEXTAUTH_URL + '/api/auth/callback/google'
      )

      oauth2Client.setCredentials({
        refresh_token: user.gmailRefreshToken
      })

      const client: GoogleClient = {
        userEmail: user.email || '',
        auth: oauth2Client
      }

      // Add Gmail client if connected
      if (user.gmailConnected && user.gmailRefreshToken) {
        client.gmail = google.gmail({ version: 'v1', auth: oauth2Client })
      }

      // Add Calendar client (using same token for now)
      if (user.gmailConnected && user.gmailRefreshToken) {
        client.calendar = google.calendar({ version: 'v3', auth: oauth2Client })
      }

      return client
    } catch (error) {
      console.error('Error creating unified Google client:', error)
      throw error
    }
  }

  /**
   * Test Gmail connection
   */
  async testGmailConnection(userId: string): Promise<ConnectionTestResult> {
    try {
      const client = await this.getGmailClient(userId)
      if (!client || !client.gmail) {
        return { connected: false, error: 'Gmail client not available' }
      }

      // Try to get user profile to test connection
      await client.gmail.users.getProfile({ userId: 'me' })

      return { connected: true }
    } catch (error) {
      console.error('Gmail connection test failed:', error)
      return {
        connected: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * Test Calendar connection
   */
  async testCalendarConnection(userId: string): Promise<ConnectionTestResult> {
    try {
      const client = await this.getCalendarClient(userId)
      if (!client || !client.calendar) {
        return { connected: false, error: 'Calendar client not available' }
      }

      // Try to get calendar list to test connection
      await client.calendar.calendarList.list({ maxResults: 1 })

      return { connected: true }
    } catch (error) {
      console.error('Calendar connection test failed:', error)
      return {
        connected: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * Get user's Gmail connection status
   */
  async getConnectionStatus(userId: string) {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        gmailConnected: true,
        gmailRefreshToken: true,
        email: true
      }
    })

    if (!user) {
      throw new Error('User not found')
    }

    return {
      gmail: {
        connected: user.gmailConnected && !!user.gmailRefreshToken,
        hasToken: !!user.gmailRefreshToken
      },
      userEmail: user.email
    }
  }

  /**
   * Disconnect Gmail service
   */
  async disconnectService(userId: string, service: GoogleService) {
    await prisma.user.update({
      where: { id: userId },
      data: {
        gmailConnected: false,
        gmailRefreshToken: null
      }
    })

    return { success: true, message: 'Gmail disconnected successfully' }
  }

  /**
   * Disconnect all services (currently just Gmail)
   */
  async disconnectAllServices(userId: string) {
    await prisma.user.update({
      where: { id: userId },
      data: {
        gmailConnected: false,
        gmailRefreshToken: null
      }
    })

    return { success: true, message: 'Gmail disconnected. Please re-authenticate.' }
  }
}

// Export singleton instance
export const unifiedGoogleClient = new UnifiedGoogleClientService()

// Export backward compatibility functions
export const getGmailClient = (userId: string) => unifiedGoogleClient.getGmailClient(userId)
export const testGmailConnection = (userId: string) => unifiedGoogleClient.testGmailConnection(userId)

// Calendar functions
export const getCalendarClient = (userId: string) => unifiedGoogleClient.getCalendarClient(userId)
export const testCalendarConnection = (userId: string) => unifiedGoogleClient.testCalendarConnection(userId)