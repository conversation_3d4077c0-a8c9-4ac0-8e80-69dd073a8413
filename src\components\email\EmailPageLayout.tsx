"use client"

import { useState, useEffect, useRef, useCallback } from "react"
import { EmailContent } from "./EmailContentWrapper"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Checkbox } from "@/components/ui/checkbox"
import { DateRangePicker } from "@/components/ui/date-range-picker"
import { DateRange } from "react-day-picker"
import { ThreadGroup } from "./types"
import {
  Inbox,
  Search,
  Calendar,
  Paperclip,
  Star,
  RefreshCw,
  ChevronLeft,
  ChevronRight,
  AlertCircle,
  Archive,
  Trash2,
  Mail,
  CheckSquare,
  Square,
  Hash,
  Folder,
  Flag
} from "lucide-react"
import { SidebarTrigger } from "@/components/ui/sidebar"

interface Email {
  id: string
  threadId: string
  from: string
  subject: string
  date: Date
  snippet: string
  isRead: boolean
  isStarred: boolean
  hasAttachments: boolean
  labels?: Array<{
    id: string
    name: string
    type: string
  }>
  body?: string
  attachments?: Array<{
    filename: string
    mimeType: string
    size: number
    attachmentId: string
  }>
  profilePhoto?: string
}

interface EmailPageLayoutProps {
  title: string
  icon: any
  apiEndpoint: string
  emptyMessage?: string
  showDateRange?: boolean
  pageContext?: 'inbox' | 'archive' | 'trash' | 'spam' | 'sent' | 'starred' | 'categories' | 'drafts'
}

// Define label colors and icons
const getLabelStyle = (labelName: string, labelType: string) => {
  if (!labelName || typeof labelName !== 'string') {
    return { bg: 'bg-gray-100', text: 'text-gray-700', border: 'border-gray-200', icon: Folder }
  }

  const name = labelName.toUpperCase()
  
  // System labels
  if (labelType === 'system') {
    switch (name) {
      case 'IMPORTANT':
        return { bg: 'bg-yellow-100', text: 'text-yellow-700', border: 'border-yellow-200', icon: Flag }
      case 'STARRED':
        return { bg: 'bg-blue-100', text: 'text-blue-700', border: 'border-blue-200', icon: Star }
      case 'UNREAD':
        return { bg: 'bg-purple-100', text: 'text-purple-700', border: 'border-purple-200', icon: Mail }
      case 'INBOX':
        return { bg: 'bg-gray-100', text: 'text-gray-700', border: 'border-gray-200', icon: Inbox }
      case 'SENT':
        return { bg: 'bg-green-100', text: 'text-green-700', border: 'border-green-200', icon: Mail }
      case 'DRAFT':
        return { bg: 'bg-orange-100', text: 'text-orange-700', border: 'border-orange-200', icon: Mail }
      case 'SPAM':
        return { bg: 'bg-red-100', text: 'text-red-700', border: 'border-red-200', icon: AlertCircle }
      case 'TRASH':
        return { bg: 'bg-red-100', text: 'text-red-700', border: 'border-red-200', icon: Trash2 }
      default:
        return { bg: 'bg-gray-100', text: 'text-gray-700', border: 'border-gray-200', icon: Hash }
    }
  }
  
  // User labels - use a hash of the label name to assign consistent colors
  const colors = [
    { bg: 'bg-indigo-100', text: 'text-indigo-700', border: 'border-indigo-200' },
    { bg: 'bg-pink-100', text: 'text-pink-700', border: 'border-pink-200' },
    { bg: 'bg-emerald-100', text: 'text-emerald-700', border: 'border-emerald-200' },
    { bg: 'bg-amber-100', text: 'text-amber-700', border: 'border-amber-200' },
    { bg: 'bg-cyan-100', text: 'text-cyan-700', border: 'border-cyan-200' },
    { bg: 'bg-rose-100', text: 'text-rose-700', border: 'border-rose-200' },
    { bg: 'bg-violet-100', text: 'text-violet-700', border: 'border-violet-200' },
    { bg: 'bg-teal-100', text: 'text-teal-700', border: 'border-teal-200' }
  ]
  
  const hash = labelName.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0)
  const colorIndex = hash % colors.length
  
  return { ...colors[colorIndex], icon: labelType === 'user' ? Hash : Folder }
}

// Component for displaying labels as icons
const LabelIcons = ({ labels }: { labels: Email['labels'] }) => {
  if (!labels || labels.length === 0) return null

  // Filter out system labels we don't want to show and ensure label has valid name
  const displayLabels = labels.filter(label =>
    label &&
    label.name &&
    typeof label.name === 'string' &&
    (!['INBOX', 'UNREAD', 'STARRED'].includes(label.name.toUpperCase()) || label.type === 'user')
  )
  
  if (displayLabels.length === 0) return null
  
  return (
    <div className="flex items-center gap-1" onClick={(e) => e.stopPropagation()}>
      {displayLabels.map((label) => {
        const style = getLabelStyle(label.name, label.type)
        const Icon = style.icon
        
        return (
          <div
            key={label.id}
            className={`
              relative group/label flex items-center justify-center h-5 w-5 rounded cursor-default
              ${style.bg} ${style.border}
              transition-all duration-200
            `}
          >
            <Icon className={`h-3 w-3 ${style.text}`} />
            
            {/* Tooltip - only shows on direct icon hover */}
            <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-900 text-white text-xs rounded whitespace-nowrap opacity-0 group-hover/label:opacity-100 pointer-events-none transition-opacity duration-200 z-50">
              {label.name}
              <div className="absolute top-full left-1/2 transform -translate-x-1/2 -mt-1 w-0 h-0 border-l-4 border-r-4 border-t-4 border-l-transparent border-r-transparent border-t-gray-900"></div>
            </div>
          </div>
        )
      })}
    </div>
  )
}

export function EmailPageLayout({ 
  title, 
  icon: Icon, 
  apiEndpoint, 
  emptyMessage = "No emails found",
  showDateRange = true,
  pageContext = 'inbox'
}: EmailPageLayoutProps) {
  // Get default date range (past 7 days)
  const getDefaultDateRange = (): DateRange => {
    const today = new Date()
    today.setHours(23, 59, 59, 999) // End of today
    
    const sevenDaysAgo = new Date()
    sevenDaysAgo.setDate(today.getDate() - 7)
    sevenDaysAgo.setHours(0, 0, 0, 0) // Start of the day 7 days ago
    
    return { from: sevenDaysAgo, to: today }
  }

  const [emails, setEmails] = useState<Email[]>([])
  const [selectedEmailId, setSelectedEmailId] = useState<string | null>(null)
  const [selectedEmails, setSelectedEmails] = useState<Set<string>>(new Set())
  const [loading, setLoading] = useState(true)
  const [dateRangeLoading, setDateRangeLoading] = useState(false)
  const [searchQuery, setSearchQuery] = useState("")
  const [dateRange, setDateRange] = useState<DateRange | undefined>(
    showDateRange ? getDefaultDateRange() : undefined
  )
  const [showThreads, setShowThreads] = useState(true) // Toggle between thread and individual email view

  const [totalCount, setTotalCount] = useState(0)
  const [requiresReconnection, setRequiresReconnection] = useState(false)
  const [currentPage, setCurrentPage] = useState(1)
  const [emailsPerPage] = useState(50)
  const [totalPages, setTotalPages] = useState(1)
  const [processingEmailId, setProcessingEmailId] = useState<string | null>(null)

  // Resizable panel state
  const [leftPanelWidth, setLeftPanelWidth] = useState(420)
  const [isResizing, setIsResizing] = useState(false)
  const containerRef = useRef<HTMLDivElement>(null)
  const resizerRef = useRef<HTMLDivElement>(null)

  // Min and max widths for the left panel
  const minLeftWidth = 300
  const maxLeftWidth = 800

  useEffect(() => {
    console.log('Date range changed, fetching emails:', dateRange)
    if (dateRange) {
      setDateRangeLoading(true)
    }
    fetchEmails(1)
  }, [dateRange])

  useEffect(() => {
    // Reset selection when emails change
    setSelectedEmails(new Set())
  }, [emails])

  const fetchEmails = async (page: number = 1) => {
    try {
      setLoading(true)
      
      const params = new URLSearchParams({
        page: page.toString(),
        limit: emailsPerPage.toString()
      })

      // Add date range parameters only if dateRange is defined
      if (dateRange?.from) {
        params.append('dateFrom', dateRange.from.toISOString())
        console.log('Adding dateFrom parameter:', dateRange.from.toISOString())
      }
      if (dateRange?.to) {
        params.append('dateTo', dateRange.to.toISOString())
        console.log('Adding dateTo parameter:', dateRange.to.toISOString())
      }

      console.log('Fetching emails with params:', params.toString())
      console.log('Date range is:', dateRange ? 'defined' : 'undefined (showing all emails)')

      // Properly construct URL by checking if apiEndpoint already has query parameters
      const separator = apiEndpoint.includes('?') ? '&' : '?'
      const response = await fetch(`${apiEndpoint}${separator}${params.toString()}`)
      const data = await response.json()

      if (!response.ok) {
        if (data.requiresReconnection) {
          setRequiresReconnection(true)
        }
        throw new Error(data.error || 'Failed to fetch emails')
      }

      // Update state
      setEmails(data.emails || [])
      setTotalCount(data.totalCount || 0)
      setTotalPages(Math.ceil((data.totalCount || 0) / emailsPerPage))
      setCurrentPage(page)
      setRequiresReconnection(false)
      
    } catch (error) {
      console.error('Error fetching emails:', error)
    } finally {
      setLoading(false)
      setDateRangeLoading(false)
    }
  }

  const toggleEmailSelection = (emailId: string) => {
    const newSelection = new Set(selectedEmails)
    if (newSelection.has(emailId)) {
      newSelection.delete(emailId)
    } else {
      newSelection.add(emailId)
    }
    setSelectedEmails(newSelection)
  }

  const selectAllEmails = () => {
    const newSelection = new Set(filteredEmails.map(email => email.id))
    setSelectedEmails(newSelection)
  }

  const clearSelection = () => {
    setSelectedEmails(new Set())
  }

  const handleEmailAction = async (emailId: string, action: string) => {
    setProcessingEmailId(emailId)
    try {
      let operation = action
      
      // Map context-specific actions to Gmail operations
      if (action === 'moveToInbox') {
        operation = 'moveToInbox'
      } else if (action === 'deletePermanently') {
        operation = 'deletePermanently'
      } else if (action === 'notSpam') {
        operation = 'notSpam'
      }

      if (action === 'star') {
        // Find the email and toggle its star state
        const email = emails.find(e => e.id === emailId)
        if (email) {
          const response = await fetch('/api/gmail/inbox/bulk', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ 
              messageIds: [emailId],
              operation: email.isStarred ? 'removeStar' : 'addStar'
            })
          })
          
          if (response.ok) {
            // Update the email in the local state
            setEmails(prevEmails => prevEmails.map(e => 
              e.id === emailId ? { ...e, isStarred: !e.isStarred } : e
            ))
            
            // Trigger custom event to notify EmailContentWrapper
            window.dispatchEvent(new CustomEvent('emailUpdated', { detail: { emailId } }))
          }
        }
      } else {
        // Handle all other operations
        let bulkApiEndpoint = '/api/gmail/inbox/bulk'
        
        // Use appropriate bulk endpoint based on context
        if (pageContext === 'categories') {
          // For categories, we use the modify endpoint for archive/trash operations
          if (action === 'archive') {
            const response = await fetch('/api/gmail/modify', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({
                emailIds: [emailId],
                addLabels: [],
                removeLabels: ['INBOX']
              })
            })
            
            if (response.ok) {
              await fetchEmails(currentPage)
            }
            return
          } else if (action === 'trash') {
            const response = await fetch('/api/gmail/modify', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({
                emailIds: [emailId],
                addLabels: ['TRASH'],
                removeLabels: []
              })
            })
            
            if (response.ok) {
              await fetchEmails(currentPage)
            }
            return
          }
        }
        
        // For other contexts, use the regular bulk endpoint
        const response = await fetch(bulkApiEndpoint, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ 
            messageIds: [emailId],
            operation: operation
          })
        })
        
        if (response.ok) {
          await fetchEmails(currentPage)
        }
      }
    } catch (error) {
      console.error(`Error performing ${action}:`, error)
    } finally {
      setProcessingEmailId(null)
    }
  }

  const markAsRead = async (emailId: string) => {
    // Implementation for marking as read
  }

  const formatDate = (date: Date | string) => {
    const d = new Date(date)
    const today = new Date()
    const yesterday = new Date(today)
    yesterday.setDate(yesterday.getDate() - 1)

    if (d.toDateString() === today.toDateString()) {
      return d.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
    } else if (d.toDateString() === yesterday.toDateString()) {
      return 'Yesterday'
    } else {
      return d.toLocaleDateString()
    }
  }

  const extractSenderName = (fromHeader: string) => {
    const match = fromHeader.match(/^(.+?)\s*<.*>$/)
    return match ? match[1].trim().replace(/"/g, '') : fromHeader
  }

  // Thread grouping logic
  const groupEmailsByThread = (emails: Email[]) => {
    const threadMap = new Map<string, Email[]>()

    // Group emails by threadId
    emails.forEach(email => {
      const threadId = email.threadId
      if (!threadMap.has(threadId)) {
        threadMap.set(threadId, [])
      }
      threadMap.get(threadId)!.push(email)
    })

    // Convert to thread group array
    return Array.from(threadMap.entries()).map(([threadId, messages]) => {
      // Sort messages by date (newest first for latest message)
      const sortedMessages = messages.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
      const latestMessage = sortedMessages[0]

      // Check if thread has unread messages
      const hasUnread = messages.some(msg => !msg.isRead)

      // Extract unique participants (from and to addresses)
      const participantSet = new Set<string>()
      messages.forEach(msg => {
        participantSet.add(extractSenderName(msg.from))
        // Could also extract 'to' addresses if available in the Email interface
      })

      return {
        threadId,
        messages: sortedMessages,
        latestMessage,
        messageCount: messages.length,
        hasUnread,
        participants: Array.from(participantSet)
      }
    }).sort((a, b) => new Date(b.latestMessage.date).getTime() - new Date(a.latestMessage.date).getTime())
  }

  // Filter emails based on search query
  const filteredEmails = emails.filter(email =>
    email.subject.toLowerCase().includes(searchQuery.toLowerCase()) ||
    email.from.toLowerCase().includes(searchQuery.toLowerCase()) ||
    email.snippet.toLowerCase().includes(searchQuery.toLowerCase())
  )

  // Get thread groups for display
  const threadGroups = showThreads ? groupEmailsByThread(filteredEmails) : []

  // Determine what to display: threads or individual emails
  const displayItems = showThreads ? threadGroups : filteredEmails

  // Resizing functionality
  const startResizing = useCallback((e: React.MouseEvent) => {
    e.preventDefault()
    setIsResizing(true)
  }, [])

  const stopResizing = useCallback(() => {
    setIsResizing(false)
  }, [])

  const resize = useCallback(
    (e: MouseEvent) => {
      if (isResizing && containerRef.current) {
        const containerRect = containerRef.current.getBoundingClientRect()
        const newWidth = e.clientX - containerRect.left
        
        // Constrain the width within min and max bounds
        const constrainedWidth = Math.min(Math.max(newWidth, minLeftWidth), maxLeftWidth)
        setLeftPanelWidth(constrainedWidth)
      }
    },
    [isResizing, minLeftWidth, maxLeftWidth]
  )

  // Mouse event listeners for resizing
  useEffect(() => {
    if (isResizing) {
      document.addEventListener('mousemove', resize)
      document.addEventListener('mouseup', stopResizing)
      document.body.style.cursor = 'col-resize'
      document.body.style.userSelect = 'none'
    } else {
      document.removeEventListener('mousemove', resize)
      document.removeEventListener('mouseup', stopResizing)
      document.body.style.cursor = ''
      document.body.style.userSelect = ''
    }

    return () => {
      document.removeEventListener('mousemove', resize)
      document.removeEventListener('mouseup', stopResizing)
      document.body.style.cursor = ''
      document.body.style.userSelect = ''
    }
  }, [isResizing, resize, stopResizing])

  return (
    <div className="h-screen flex flex-col bg-white overflow-hidden">
      {/* Two-Panel Layout with Fixed Height and Resizable Splitter */}
      <div ref={containerRef} className="flex-1 flex min-h-0 overflow-hidden">
        {/* Left Panel - Email List with Fixed Controls and Scrollable Content */}
        <div 
          className="border-r border-gray-100 flex flex-col h-full"
          style={{ width: `${leftPanelWidth}px` }}
        >
          {/* Fixed Header Section */}
          <div className="flex-shrink-0 bg-white border-b border-gray-100">
            {/* Title and Count */}
            <div className="p-4 border-b border-gray-100">
              <div className="flex items-center gap-3">
                <Icon className="h-5 w-5 text-gray-600" />
                <h1 className="text-lg font-semibold text-gray-900">{title}</h1>
                <Badge variant="secondary" className="ml-auto">
                  {totalCount}
                </Badge>
              </div>
            </div>
            
            {/* Search and Date Range */}
            <div className="p-4 space-y-3 border-b border-gray-100">
              {/* Search Bar and Thread Toggle */}
              <div className="flex gap-2">
                <div className="relative flex-1">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="Search emails..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10 border-gray-200 bg-gray-50 focus:bg-white transition-colors"
                  />
                </div>
                <Button
                  variant={showThreads ? "default" : "outline"}
                  size="sm"
                  onClick={() => setShowThreads(!showThreads)}
                  className="px-3 whitespace-nowrap"
                  title={showThreads ? "Switch to individual emails" : "Switch to conversation view"}
                >
                  {showThreads ? "Conversations" : "Individual"}
                </Button>
              </div>
              
              {/* Date Range Picker */}
              {showDateRange && (
                <div className="relative">
                  <DateRangePicker
                    dateRange={dateRange}
                    onDateRangeChange={(range) => {
                      setDateRange(range)
                      setCurrentPage(1)
                    }}
                    placeholder="Select date range"
                    className="w-full"
                  />
                  {dateRangeLoading && (
                    <div className="absolute right-2 top-1/2 transform -translate-y-1/2">
                      <RefreshCw className="h-4 w-4 animate-spin text-blue-500" />
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* Controls Row */}
            <div className="px-4 py-2 border-b border-gray-100 flex items-center justify-between">
              <SidebarTrigger className="h-8 w-8 p-0" />

              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  if (selectedEmails.size === filteredEmails.length && filteredEmails.length > 0) {
                    clearSelection()
                  } else {
                    selectAllEmails()
                  }
                }}
                className="flex items-center gap-2 text-gray-600 hover:text-gray-900 h-8"
              >
                {selectedEmails.size === filteredEmails.length && filteredEmails.length > 0 ? (
                  <CheckSquare className="h-4 w-4" />
                ) : (
                  <Square className="h-4 w-4" />
                )}
                <span className="text-xs">
                  {selectedEmails.size > 0 ? `${selectedEmails.size} selected` : 'Select all'}
                </span>
              </Button>

              <Button
                variant="ghost"
                size="sm"
                onClick={() => fetchEmails(currentPage)}
                disabled={loading}
                className="text-gray-600 hover:text-gray-900 h-8 w-8 p-0"
              >
                <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
              </Button>
            </div>

            {/* Date Range Filter Indicator */}
            {showDateRange && (
              <div className={`px-4 py-2 border-b flex items-center justify-between text-sm ${
                dateRange 
                  ? 'bg-blue-50/50 border-blue-100' 
                  : 'bg-gray-50/50 border-gray-100'
              }`}>
                <div className={`flex items-center gap-2 ${
                  dateRange ? 'text-blue-700' : 'text-gray-600'
                }`}>
                  <Calendar className="h-4 w-4" />
                  <span>
                    {dateRange ? (
                      <>
                        Showing emails from {dateRange.from ? dateRange.from.toLocaleDateString() : 'start'} 
                        {dateRange.to && ` to ${dateRange.to.toLocaleDateString()}`}
                      </>
                    ) : (
                      'Showing all emails (no date filter)'
                    )}
                  </span>
                </div>
                {dateRange && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setDateRange(undefined)}
                    className="h-6 px-2 text-blue-600 hover:text-blue-800 hover:bg-blue-100"
                  >
                    Clear filter
                  </Button>
                )}
              </div>
            )}
          </div>

          {/* Scrollable Email List Content */}
          {requiresReconnection ? (
            <div className="flex-1 flex items-center justify-center p-8 text-center">
              <div>
                <AlertCircle className="h-8 w-8 mx-auto mb-4 text-amber-500" />
                <p className="text-gray-600 mb-4">Please reconnect your Gmail account</p>
                <Button
                  onClick={() => window.location.href = '/api/gmail/connect'}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  Reconnect Gmail
                </Button>
              </div>
            </div>
          ) : loading && emails.length === 0 ? (
            <div className="flex-1 flex items-center justify-center">
              <RefreshCw className="h-6 w-6 animate-spin text-gray-400" />
            </div>
          ) : filteredEmails.length === 0 ? (
            <div className="flex-1 flex items-center justify-center text-gray-500">
              <div className="text-center">
                <Icon className="h-8 w-8 mx-auto mb-4 text-gray-300" />
                <p>{searchQuery ? 'No emails match your search' : emptyMessage}</p>
              </div>
            </div>
          ) : (
            <div className="flex-1 overflow-y-auto overflow-x-hidden">
              {showThreads ? (
                // Thread view
                threadGroups.map((thread, index) => (
                  <div
                    key={thread.threadId}
                    className={`group relative px-4 py-3 transition-all duration-200 hover:bg-gray-50 cursor-pointer ${
                      selectedEmailId === thread.latestMessage.id ? 'bg-blue-50 border-l-2 border-blue-500' : ''
                    } ${selectedEmails.has(thread.latestMessage.id) ? 'bg-blue-50/30' : ''} ${
                      processingEmailId === thread.latestMessage.id ? 'opacity-75' : ''
                    } ${index === 0 ? '' : 'border-t border-gray-50'}`}
                  >
                    <div className="flex items-start gap-3">
                      <Checkbox
                        checked={selectedEmails.has(thread.latestMessage.id)}
                        onCheckedChange={() => toggleEmailSelection(thread.latestMessage.id)}
                        className="mt-2"
                        onClick={(e) => e.stopPropagation()}
                      />

                      <Avatar className="w-8 h-8 mt-1">
                        {thread.latestMessage.profilePhoto && (
                          <AvatarImage
                            src={thread.latestMessage.profilePhoto}
                            alt={extractSenderName(thread.latestMessage.from)}
                            className="object-cover"
                          />
                        )}
                        <AvatarFallback className="text-xs font-medium bg-gray-100 text-gray-700">
                          {extractSenderName(thread.latestMessage.from).charAt(0).toUpperCase()}
                        </AvatarFallback>
                      </Avatar>

                      <div
                        className="flex-1 min-w-0"
                        onClick={() => {
                          if (!thread.latestMessage.isRead) {
                            markAsRead(thread.latestMessage.id)
                          }
                          setSelectedEmailId(thread.latestMessage.id)
                        }}
                      >
                        <div className="flex items-center justify-between mb-1">
                          <div className="flex items-center gap-2">
                            <span className={`text-sm ${thread.hasUnread ? 'font-semibold text-gray-900' : 'font-medium text-gray-700'}`}>
                              {thread.participants.length > 1 ? thread.participants.slice(0, 2).join(', ') + (thread.participants.length > 2 ? '...' : '') : extractSenderName(thread.latestMessage.from)}
                            </span>
                            {thread.messageCount > 1 && (
                              <Badge variant="secondary" className="text-xs px-1.5 py-0.5 h-5">
                                {thread.messageCount}
                              </Badge>
                            )}
                            <LabelIcons labels={thread.latestMessage.labels} />
                            {thread.latestMessage.labels?.some(label => label && label.name === 'IMPORTANT') && (
                              <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                            )}
                          </div>
                          <span className="text-xs text-gray-500 whitespace-nowrap">
                            {formatDate(thread.latestMessage.date)}
                          </span>
                        </div>

                        <h3 className={`text-sm mb-1 truncate ${thread.hasUnread ? 'font-medium text-gray-900' : 'text-gray-700'}`}>
                          {thread.latestMessage.subject || '(No Subject)'}
                        </h3>

                        <p className="text-sm text-gray-500 line-clamp-1 truncate">
                          {thread.latestMessage.snippet}
                        </p>

                        {thread.latestMessage.hasAttachments && (
                          <div className="flex items-center gap-1 mt-1">
                            <Paperclip className="h-3 w-3 text-gray-400" />
                            <span className="text-xs text-gray-400">Attachment</span>
                          </div>
                        )}
                      </div>

                      {/* Thread actions - similar to individual email actions */}
                      <div className="absolute right-2 top-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center gap-1 bg-white/90 backdrop-blur-sm rounded-md shadow-sm border border-gray-200 p-1">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation()
                            handleEmailAction(thread.latestMessage.id, 'star')
                          }}
                          disabled={processingEmailId === thread.latestMessage.id}
                          className="h-7 w-7 p-0 hover:bg-gray-100 disabled:opacity-50"
                          title={thread.latestMessage.isStarred ? "Remove star" : "Add star"}
                        >
                          <Star className={`h-3.5 w-3.5 ${thread.latestMessage.isStarred ? 'fill-yellow-400 text-yellow-400' : 'text-gray-500'}`} />
                        </Button>
                        {/* Add other context-specific actions here */}
                      </div>
                    </div>
                  </div>
                ))
              ) : (
                // Individual email view
                filteredEmails.map((email, index) => (
                <div
                  key={email.id}
                  className={`group relative px-4 py-3 transition-all duration-200 hover:bg-gray-50 cursor-pointer ${
                    selectedEmailId === email.id ? 'bg-blue-50 border-l-2 border-blue-500' : ''
                  } ${selectedEmails.has(email.id) ? 'bg-blue-50/30' : ''} ${
                    processingEmailId === email.id ? 'opacity-75' : ''
                  } ${index === 0 ? '' : 'border-t border-gray-50'}`}
                >
                  <div className="flex items-start gap-3">
                    <Checkbox
                      checked={selectedEmails.has(email.id)}
                      onCheckedChange={() => toggleEmailSelection(email.id)}
                      className="mt-2"
                      onClick={(e) => e.stopPropagation()}
                    />

                    <Avatar className="w-8 h-8 mt-1">
                      {email.profilePhoto && (
                        <AvatarImage 
                          src={email.profilePhoto} 
                          alt={extractSenderName(email.from)}
                          className="object-cover"
                        />
                      )}
                      <AvatarFallback className="text-xs font-medium bg-gray-100 text-gray-700">
                        {extractSenderName(email.from).charAt(0).toUpperCase()}
                      </AvatarFallback>
                    </Avatar>

                    <div 
                      className="flex-1 min-w-0"
                      onClick={() => {
                        if (!email.isRead) {
                          markAsRead(email.id)
                        }
                        setSelectedEmailId(email.id)
                      }}
                    >
                      <div className="flex items-center justify-between mb-1">
                        <div className="flex items-center gap-2">
                          <span className={`text-sm ${!email.isRead ? 'font-semibold text-gray-900' : 'font-medium text-gray-700'}`}>
                            {extractSenderName(email.from)}
                          </span>
                          <LabelIcons labels={email.labels} />
                          {email.labels?.some(label => label && label.name === 'IMPORTANT') && (
                            <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                          )}
                        </div>
                        <span className="text-xs text-gray-500 whitespace-nowrap">
                          {formatDate(email.date)}
                        </span>
                      </div>
                      
                      <h3 className={`text-sm mb-1 truncate ${!email.isRead ? 'font-medium text-gray-900' : 'text-gray-700'}`}>
                        {email.subject || '(No Subject)'}
                      </h3>
                      
                      <p className="text-sm text-gray-500 line-clamp-1 truncate">
                        {email.snippet}
                      </p>

                      {email.hasAttachments && (
                        <div className="flex items-center gap-1 mt-1">
                          <Paperclip className="h-3 w-3 text-gray-400" />
                          <span className="text-xs text-gray-400">Attachment</span>
                        </div>
                      )}
                    </div>

                    <div className="absolute right-2 top-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center gap-1 bg-white/90 backdrop-blur-sm rounded-md shadow-sm border border-gray-200 p-1">
                      {/* Star button - always available */}
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation()
                          handleEmailAction(email.id, 'star')
                        }}
                        disabled={processingEmailId === email.id}
                        className="h-7 w-7 p-0 hover:bg-gray-100 disabled:opacity-50"
                        title={email.isStarred ? "Remove star" : "Add star"}
                      >
                        <Star className={`h-3.5 w-3.5 ${email.isStarred ? 'fill-yellow-400 text-yellow-400' : 'text-gray-500'}`} />
                      </Button>

                      {/* Context-specific actions */}
                      {pageContext === 'inbox' && (
                        <>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation()
                              handleEmailAction(email.id, 'archive')
                            }}
                            disabled={processingEmailId === email.id}
                            className="h-7 w-7 p-0 hover:bg-gray-100 disabled:opacity-50"
                            title="Archive email"
                          >
                            <Archive className="h-3.5 w-3.5 text-gray-500" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation()
                              handleEmailAction(email.id, 'trash')
                            }}
                            disabled={processingEmailId === email.id}
                            className="h-7 w-7 p-0 hover:bg-red-100 hover:text-red-600 disabled:opacity-50"
                            title="Move to trash"
                          >
                            <Trash2 className="h-3.5 w-3.5 text-gray-500 hover:text-red-600" />
                          </Button>
                        </>
                      )}

                      {pageContext === 'archive' && (
                        <>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation()
                              handleEmailAction(email.id, 'moveToInbox')
                            }}
                            disabled={processingEmailId === email.id}
                            className="h-7 w-7 p-0 hover:bg-blue-100 hover:text-blue-600 disabled:opacity-50"
                            title="Move to inbox"
                          >
                            <Inbox className="h-3.5 w-3.5 text-gray-500 hover:text-blue-600" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation()
                              handleEmailAction(email.id, 'trash')
                            }}
                            disabled={processingEmailId === email.id}
                            className="h-7 w-7 p-0 hover:bg-red-100 hover:text-red-600 disabled:opacity-50"
                            title="Move to trash"
                          >
                            <Trash2 className="h-3.5 w-3.5 text-gray-500 hover:text-red-600" />
                          </Button>
                        </>
                      )}

                      {pageContext === 'trash' && (
                        <>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation()
                              handleEmailAction(email.id, 'moveToInbox')
                            }}
                            disabled={processingEmailId === email.id}
                            className="h-7 w-7 p-0 hover:bg-blue-100 hover:text-blue-600 disabled:opacity-50"
                            title="Move to inbox"
                          >
                            <Inbox className="h-3.5 w-3.5 text-gray-500 hover:text-blue-600" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation()
                              handleEmailAction(email.id, 'deletePermanently')
                            }}
                            disabled={processingEmailId === email.id}
                            className="h-7 w-7 p-0 hover:bg-red-100 hover:text-red-600 disabled:opacity-50"
                            title="Delete permanently"
                          >
                            <Trash2 className="h-3.5 w-3.5 text-gray-500 hover:text-red-600" />
                          </Button>
                        </>
                      )}

                      {pageContext === 'spam' && (
                        <>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation()
                              handleEmailAction(email.id, 'notSpam')
                            }}
                            disabled={processingEmailId === email.id}
                            className="h-7 w-7 p-0 hover:bg-green-100 hover:text-green-600 disabled:opacity-50"
                            title="Not spam (move to inbox)"
                          >
                            <Inbox className="h-3.5 w-3.5 text-gray-500 hover:text-green-600" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation()
                              handleEmailAction(email.id, 'deletePermanently')
                            }}
                            disabled={processingEmailId === email.id}
                            className="h-7 w-7 p-0 hover:bg-red-100 hover:text-red-600 disabled:opacity-50"
                            title="Delete permanently"
                          >
                            <Trash2 className="h-3.5 w-3.5 text-gray-500 hover:text-red-600" />
                          </Button>
                        </>
                      )}

                      {(pageContext === 'sent' || pageContext === 'starred' || pageContext === 'categories') && (
                        <>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation()
                              handleEmailAction(email.id, 'archive')
                            }}
                            disabled={processingEmailId === email.id}
                            className="h-7 w-7 p-0 hover:bg-gray-100 disabled:opacity-50"
                            title="Archive email"
                          >
                            <Archive className="h-3.5 w-3.5 text-gray-500" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation()
                              handleEmailAction(email.id, 'trash')
                            }}
                            disabled={processingEmailId === email.id}
                            className="h-7 w-7 p-0 hover:bg-red-100 hover:text-red-600 disabled:opacity-50"
                            title="Move to trash"
                          >
                            <Trash2 className="h-3.5 w-3.5 text-gray-500 hover:text-red-600" />
                          </Button>
                        </>
                      )}
                    </div>
                  </div>
                </div>
                ))
              )}
            </div>
          )}

          {/* Fixed Pagination Controls */}
          {totalPages > 1 && (
            <div className="flex-shrink-0 p-3 border-t border-gray-100 flex items-center justify-between bg-white">
              <div className="text-xs text-gray-500">
                Showing {((currentPage - 1) * emailsPerPage) + 1} - {Math.min(currentPage * emailsPerPage, totalCount)} of {totalCount} emails
              </div>
              <div className="flex items-center gap-1">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => fetchEmails(currentPage - 1)}
                  disabled={currentPage <= 1 || loading}
                  className="h-8 w-8 p-0"
                >
                  <ChevronLeft className="h-4 w-4" />
                </Button>
                <span className="text-xs text-gray-500 px-2">
                  {currentPage} of {totalPages}
                </span>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => fetchEmails(currentPage + 1)}
                  disabled={currentPage >= totalPages || loading}
                  className="h-8 w-8 p-0"
                >
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </div>
            </div>
          )}
        </div>

        {/* Resizable Splitter */}
        <div
          ref={resizerRef}
          className={`w-2 bg-gray-100 hover:bg-gray-200 cursor-col-resize flex items-center justify-center group transition-colors ${
            isResizing ? 'bg-blue-200' : ''
          }`}
          onMouseDown={startResizing}
          title="Drag to resize panels"
        >
          <div className={`w-0.5 h-8 bg-gray-300 group-hover:bg-gray-400 transition-colors ${
            isResizing ? 'bg-blue-400' : ''
          }`} />
        </div>

        {/* Right Panel - Email Content */}
        <div className="flex-1 min-h-0 overflow-hidden">
          <EmailContent 
            emailId={selectedEmailId} 
            onEmailUpdate={() => fetchEmails(currentPage)}
          />
        </div>
      </div>

      {/* Loading Indicator */}
      {processingEmailId && (
        <div className="fixed bottom-4 right-4 bg-blue-600 text-white px-4 py-2 rounded-lg shadow-lg flex items-center gap-2 z-50">
          <RefreshCw className="w-4 h-4 animate-spin" />
          <span className="text-sm">Processing...</span>
        </div>
      )}
    </div>
  )
}
