import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from "next-auth/next"
import { authOptions } from '@/lib/auth'
import { aiAgentTaskService } from '@/lib/ai/aiAgentTasks'

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions) as any
    
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const {
      context,
      emailContext
    } = await request.json()

    if (!context) {
      return NextResponse.json({ 
        error: 'Context is required for task suggestions' 
      }, { status: 400 })
    }

    const result = await aiAgentTaskService.suggestTasks(
      session.user.id,
      context,
      emailContext
    )

    return NextResponse.json({
      ...result,
      success: true
    })

  } catch (error) {
    console.error('Task suggestions error:', error)
    return NextResponse.json(
      { error: 'Failed to get task suggestions' },
      { status: 500 }
    )
  }
}
