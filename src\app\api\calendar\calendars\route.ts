import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from "next-auth/next"
import { authOptions } from '@/lib/auth'
import { getCalendarClient } from '@/lib/unified-google-client'
import { unifiedCacheService } from '@/lib/cache/unifiedCacheService'
import { generateCalendarListCacheKey } from '@/lib/cache/utils/calendarConversion'

// GET /api/calendar/calendars - List all calendars
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions) as any
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const minAccessRole = searchParams.get('minAccessRole')
    const maxResults = parseInt(searchParams.get('maxResults') || '250')
    const showDeleted = searchParams.get('showDeleted') === 'true'
    const showHidden = searchParams.get('showHidden') === 'true'

    // Create cache key based on parameters
    const cacheKey = `calendars_${minAccessRole || 'all'}_${maxResults}_${showDeleted}_${showHidden}`

    // Try to get from cache first
    const cachedResult = unifiedCacheService.getCachedCalendarList(session.user.id)
    if (cachedResult.success && cachedResult.data) {
      console.log('Calendar List API - Returning cached data')
      return NextResponse.json({
        calendars: cachedResult.data,
        count: cachedResult.data.length,
        cached: true
      })
    }

    try {
      const { calendar } = await getCalendarClient(session.user.id)

      const listParams: any = {
        maxResults,
        showDeleted,
        showHidden
      }

      if (minAccessRole) {
        listParams.minAccessRole = minAccessRole
      }

      const response = await calendar.calendarList.list(listParams)
      const calendars = response.data.items || []

      // Cache the calendars
      if (calendars.length > 0) {
        unifiedCacheService.cacheCalendarList(session.user.id, calendars)
        console.log(`📅 Cached ${calendars.length} calendars for user: ${session.user.id}`)
      }

      return NextResponse.json({
        calendars: calendars,
        count: calendars.length,
        nextPageToken: response.data.nextPageToken
      })

    } catch (error) {
      console.error('Google Calendar list error:', error)
      return NextResponse.json(
        { error: 'Failed to fetch calendars. Please reconnect your Google Calendar.' },
        { status: 403 }
      )
    }

  } catch (error) {
    console.error('Calendar list error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch calendars' },
      { status: 500 }
    )
  }
}

// POST /api/calendar/calendars - Create a new calendar
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions) as any
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const calendarData = await request.json()

    // Validate required fields
    if (!calendarData.summary) {
      return NextResponse.json({ error: 'Calendar summary is required' }, { status: 400 })
    }

    try {
      const { calendar } = await getCalendarClient(session.user.id)

      const newCalendar = {
        summary: calendarData.summary,
        description: calendarData.description,
        location: calendarData.location,
        timeZone: calendarData.timeZone || 'UTC'
      }

      const response = await calendar.calendars.insert({
        requestBody: newCalendar
      })

      // Invalidate calendar cache after creating calendar
      unifiedCacheService.invalidateCache({
        module: 'calendar',
        userId: session.user.id,
        pattern: 'calendars:list'
      })

      return NextResponse.json({
        calendar: response.data,
        message: 'Calendar created successfully'
      })

    } catch (error) {
      console.error('Google Calendar create error:', error)
      return NextResponse.json(
        { error: 'Failed to create calendar. Please check your permissions.' },
        { status: 403 }
      )
    }

  } catch (error) {
    console.error('Calendar create error:', error)
    return NextResponse.json(
      { error: 'Failed to create calendar' },
      { status: 500 }
    )
  }
}

// PUT /api/calendar/calendars?calendarId=xxx - Update a calendar
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions) as any
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const calendarId = searchParams.get('calendarId')

    if (!calendarId) {
      return NextResponse.json({ error: 'Calendar ID is required' }, { status: 400 })
    }

    const calendarData = await request.json()

    try {
      const { calendar } = await getCalendarClient(session.user.id)

      // Get existing calendar first
      const existingCalendar = await calendar.calendars.get({
        calendarId: calendarId
      })

      // Update the calendar with new data
      const updatedCalendar = {
        ...existingCalendar.data,
        summary: calendarData.summary !== undefined ? calendarData.summary : existingCalendar.data.summary,
        description: calendarData.description !== undefined ? calendarData.description : existingCalendar.data.description,
        location: calendarData.location !== undefined ? calendarData.location : existingCalendar.data.location,
        timeZone: calendarData.timeZone !== undefined ? calendarData.timeZone : existingCalendar.data.timeZone
      }

      const response = await calendar.calendars.update({
        calendarId: calendarId,
        requestBody: updatedCalendar
      })

      // Invalidate calendar cache after updating calendar
      unifiedCacheService.invalidateCache({
        module: 'calendar',
        userId: session.user.id,
        pattern: 'calendars:list'
      })

      return NextResponse.json({
        calendar: response.data,
        message: 'Calendar updated successfully'
      })

    } catch (error) {
      console.error('Google Calendar update error:', error)
      return NextResponse.json(
        { error: 'Failed to update calendar. Please check your permissions.' },
        { status: 403 }
      )
    }

  } catch (error) {
    console.error('Calendar update error:', error)
    return NextResponse.json(
      { error: 'Failed to update calendar' },
      { status: 500 }
    )
  }
}

// DELETE /api/calendar/calendars?calendarId=xxx - Delete a calendar
export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions) as any
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const calendarId = searchParams.get('calendarId')

    if (!calendarId) {
      return NextResponse.json({ error: 'Calendar ID is required' }, { status: 400 })
    }

    if (calendarId === 'primary') {
      return NextResponse.json({ error: 'Cannot delete primary calendar' }, { status: 400 })
    }

    try {
      const { calendar } = await getCalendarClient(session.user.id)

      await calendar.calendars.delete({
        calendarId: calendarId
      })

      // Invalidate calendar cache after deleting calendar
      unifiedCacheService.invalidateCache({
        module: 'calendar',
        userId: session.user.id,
        pattern: 'calendars:list'
      })

      return NextResponse.json({
        message: 'Calendar deleted successfully'
      })

    } catch (error) {
      console.error('Google Calendar delete error:', error)
      return NextResponse.json(
        { error: 'Failed to delete calendar. Please check your permissions.' },
        { status: 403 }
      )
    }

  } catch (error) {
    console.error('Calendar delete error:', error)
    return NextResponse.json(
      { error: 'Failed to delete calendar' },
      { status: 500 }
    )
  }
} 