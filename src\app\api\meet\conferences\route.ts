import { NextRequest, NextResponse } from "next/server"
import {
  authenticateMeetUser,
  handleMeetApiError,
  formatMeetApiResponse,
  validatePaginationParams,
  validateDateRange,
  buildMeetFilter,
  listConferenceRecords,
  getRecentConferenceRecords,
  getConferenceStatistics,
  searchConferenceRecords
} from "@/lib/meet"
import { unifiedCacheService } from '@/lib/cache/unifiedCacheService'

/**
 * GET /api/meet/conferences - List conference records with filtering
 */
export async function GET(request: NextRequest) {
  try {
    const authResult = await authenticateMeetUser(request)
    if (!authResult.success) {
      return authResult.response
    }

    const { user, url } = authResult.data
    const searchParams = url.searchParams

    // Handle different query types
    const queryType = searchParams.get('type') || 'list'

    switch (queryType) {
      case 'recent':
        return await handleRecentConferences(user.id, searchParams)
      
      case 'statistics':
        return await handleConferenceStatistics(user.id, searchParams)
      
      case 'search':
        return await handleConferenceSearch(user.id, searchParams)
      
      case 'list':
      default:
        return await handleListConferences(user.id, searchParams)
    }
  } catch (error) {
    return handleMeetApiError(error)
  }
}

async function handleListConferences(userId: string, searchParams: URLSearchParams) {
  const { pageSize, pageToken } = validatePaginationParams(searchParams)
  const filter = buildMeetFilter(searchParams)

  // Create cache key based on parameters
  const cacheKey = `conferences_${userId}_${pageSize}_${pageToken || 'first'}_${JSON.stringify(filter)}`

  // Try to get from cache first - using generic cache for conference lists
  const cachedResult = unifiedCacheService.getCachedData('meet', cacheKey, userId)
  if (cachedResult.success && cachedResult.data) {
    console.log('Meet Conferences API - Returning cached data')
    return formatMeetApiResponse(
      cachedResult.data,
      "Conference records retrieved successfully (cached)"
    )
  }

  const conferences = await listConferenceRecords(userId, {
    pageSize,
    pageToken,
    filter
  })

  // Cache the conference records
  if (conferences) {
    unifiedCacheService.cacheData('meet', cacheKey, conferences, userId)
    console.log(`🎥 Cached ${conferences.length || 0} conference records for user: ${userId}`)
  }

  return formatMeetApiResponse(
    conferences,
    "Conference records retrieved successfully"
  )
}

async function handleRecentConferences(userId: string, searchParams: URLSearchParams) {
  const days = parseInt(searchParams.get('days') || '7')
  const pageSize = parseInt(searchParams.get('pageSize') || '20')

  if (days < 1 || days > 365) {
    return NextResponse.json({
      error: "Days parameter must be between 1 and 365"
    }, { status: 400 })
  }

  // Create cache key for recent conferences
  const cacheKey = `recent_conferences_${userId}_${days}_${pageSize}`

  // Try to get from cache first
  const cachedResult = unifiedCacheService.getCachedData('meet', cacheKey, userId)
  if (cachedResult.success && cachedResult.data) {
    console.log('Meet Recent Conferences API - Returning cached data')
    return formatMeetApiResponse(
      cachedResult.data,
      `Recent conference records for the last ${days} days retrieved successfully (cached)`
    )
  }

  const conferences = await getRecentConferenceRecords(userId, days, pageSize)

  // Cache the recent conference records
  if (conferences) {
    unifiedCacheService.cacheData('meet', cacheKey, conferences, userId)
    console.log(`🎥 Cached ${conferences.length || 0} recent conference records for user: ${userId}`)
  }

  return formatMeetApiResponse(
    conferences,
    `Recent conference records for the last ${days} days retrieved successfully`
  )
}

async function handleConferenceStatistics(userId: string, searchParams: URLSearchParams) {
  try {
    const { startTime, endTime } = validateDateRange(searchParams)

    // Create cache key for statistics
    const cacheKey = `statistics_${userId}_${startTime}_${endTime}`

    // Try to get from cache first
    const cachedResult = unifiedCacheService.getCachedData('meet', cacheKey, userId)
    if (cachedResult.success && cachedResult.data) {
      console.log('Meet Statistics API - Returning cached data')
      return formatMeetApiResponse(
        cachedResult.data,
        "Conference statistics retrieved successfully (cached)"
      )
    }

    const statistics = await getConferenceStatistics(userId, startTime, endTime)

    // Cache the statistics
    if (statistics) {
      unifiedCacheService.cacheData('meet', cacheKey, statistics, userId)
      console.log(`🎥 Cached meeting statistics for user: ${userId}`)
    }

    return formatMeetApiResponse(
      statistics,
      "Conference statistics retrieved successfully"
    )
  } catch (error) {
    return NextResponse.json({
      error: error instanceof Error ? error.message : "Invalid date range parameters"
    }, { status: 400 })
  }
}

async function handleConferenceSearch(userId: string, searchParams: URLSearchParams) {
  const { pageSize, pageToken } = validatePaginationParams(searchParams)
  
  // Build search criteria
  const criteria: any = {}
  
  const spaceName = searchParams.get('spaceName')
  if (spaceName) criteria.spaceName = spaceName
  
  const startDate = searchParams.get('startDate')
  if (startDate) criteria.startDate = startDate
  
  const endDate = searchParams.get('endDate')
  if (endDate) criteria.endDate = endDate
  
  const minDuration = searchParams.get('minDuration')
  if (minDuration) criteria.minDuration = parseInt(minDuration)
  
  const hasRecording = searchParams.get('hasRecording')
  if (hasRecording) criteria.hasRecording = hasRecording === 'true'
  
  const hasTranscript = searchParams.get('hasTranscript')
  if (hasTranscript) criteria.hasTranscript = hasTranscript === 'true'

  const conferences = await searchConferenceRecords(userId, criteria, {
    pageSize,
    pageToken
  })

  return formatMeetApiResponse(
    conferences,
    "Conference search completed successfully"
  )
}
