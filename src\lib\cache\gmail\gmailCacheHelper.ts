/**
 * Gmail Cache Helper
 * Centralized Gmail caching functionality with optimized performance
 * Updated to use the unified cache system
 */

import {
  CachedEmail as UnifiedCachedEmail,
  CachedEmailList as UnifiedCachedEmailList
} from '@/lib/cache/unified/types'
import {
  CachedEmail as LegacyCachedEmail,
  CachedEmailList as LegacyCachedEmailList
} from '@/contexts/cache/types'
import {
  getCachedEmails as getUnifiedCachedEmails,
  setCachedEmails as setUnifiedCachedEmails,
  isEmailCacheValid as isUnifiedEmailCacheValid,
  getCachedEmailDetail as getUnifiedCachedEmailDetail,
  setCachedEmailDetail as setUnifiedCachedEmailDetail,
  updateCachedEmail as updateUnifiedCachedEmail,
  getCachedThread as getUnifiedCachedThread,
  setCachedThread as setUnifiedCachedThread,
  invalidateEmailCache as invalidateUnifiedEmailCache,
  invalidateEmailDetail as invalidateUnifiedEmailDetail,
  invalidateThread as invalidateUnifiedThread,
  cacheMultipleEmails as cacheUnifiedMultipleEmails,
  getMultipleCachedEmails as getUnifiedMultipleCachedEmails,
  preloadGmailData as preloadUnifiedGmailData,
  getGmailCacheStats as getUnifiedGmailCacheStats,
  cleanupGmailCache as cleanupUnifiedGmailCache,
  GMAIL_CACHE_KEYS as UNIFIED_GMAIL_CACHE_KEYS,
  GMAIL_CACHE_TTL as UNIFIED_GMAIL_CACHE_TTL
} from '@/lib/cache/unified'

// Type conversion functions for compatibility
function convertUnifiedToLegacyEmail(email: UnifiedCachedEmail): LegacyCachedEmail {
  return {
    ...email,
    date: new Date(email.date),
    to: email.to || [],
    cc: email.cc || [],
    bcc: email.bcc || [],
    labels: email.labels.map(labelId => ({
      id: labelId,
      name: labelId,
      type: 'system'
    })),
    attachments: email.attachments?.map(att => ({
      ...att,
      attachmentId: att.filename // Use filename as attachmentId for compatibility
    }))
  }
}

function convertLegacyToUnifiedEmail(email: LegacyCachedEmail): UnifiedCachedEmail {
  return {
    ...email,
    date: email.date instanceof Date ? email.date.toISOString() : String(email.date),
    to: email.to || [],
    cc: email.cc || [],
    bcc: email.bcc || [],
    labels: email.labels?.map(label => label.id) || [],
    attachments: email.attachments?.map(att => ({
      filename: att.filename,
      mimeType: att.mimeType,
      size: att.size
    }))
  }
}

function convertUnifiedToLegacyEmailList(list: UnifiedCachedEmailList): LegacyCachedEmailList {
  return {
    emails: list.emails.map(convertUnifiedToLegacyEmail),
    totalCount: list.totalCount,
    timestamp: Date.now(),
    currentPage: 1,
    totalPages: 1
  }
}

function convertLegacyToUnifiedEmailList(list: LegacyCachedEmailList): UnifiedCachedEmailList {
  return {
    emails: list.emails.map(convertLegacyToUnifiedEmail),
    totalCount: list.totalCount,
    nextPageToken: undefined,
    query: undefined,
    category: undefined,
    timestamp: Date.now()
  }
}

// Export types for backward compatibility
export type CachedEmail = LegacyCachedEmail
export type CachedEmailList = LegacyCachedEmailList

// ============================================================================
// CONSTANTS
// ============================================================================

export const GMAIL_CACHE_KEYS = {
  INBOX_7DAYS: 'inbox_7days',
  SENT_7DAYS: 'sent_7days',
  SPAM_7DAYS: 'spam_7days',
  TRASH_7DAYS: 'trash_7days',
  ARCHIVE_7DAYS: 'archive_7days',
  DRAFTS: 'drafts',
  CATEGORIES: 'categories',
  EMAIL_DETAILS: 'emailDetails',
  THREADS: 'threads'
} as const

export const GMAIL_CACHE_TTL = UNIFIED_GMAIL_CACHE_TTL // 2 minutes

// ============================================================================
// UNIFIED CACHE WRAPPER FUNCTIONS
// ============================================================================

/**
 * Get cached email list by type and optional category
 * Updated to use unified cache system
 */
export function getCachedEmails(type: string, category?: string, userId?: string): CachedEmailList | null {
  if (!userId) {
    console.warn('getCachedEmails: userId is required for unified cache system')
    return null
  }

  const result = getUnifiedCachedEmails(userId, type, category)
  return result.success && result.data ? convertUnifiedToLegacyEmailList(result.data) : null
}

/**
 * Set cached email list with optimized TTL
 * Updated to use unified cache system
 */
export function setCachedEmails(type: string, data: CachedEmailList, category?: string, userId?: string): void {
  if (!userId) {
    console.warn('setCachedEmails: userId is required for unified cache system')
    return
  }

  setUnifiedCachedEmails(userId, convertLegacyToUnifiedEmailList(data), type, category)
}

/**
 * Check if email cache is valid for given type and category
 * Updated to use unified cache system
 */
export function isEmailCacheValid(type: string, category?: string, _maxAge?: number, userId?: string): boolean {
  if (!userId) {
    console.warn('isEmailCacheValid: userId is required for unified cache system')
    return false
  }

  return isUnifiedEmailCacheValid(userId, type, category)
}

/**
 * Get cached email detail by ID
 * Updated to use unified cache system
 */
export function getCachedEmailDetail(emailId: string, userId?: string): CachedEmail | null {
  if (!userId) {
    console.warn('getCachedEmailDetail: userId is required for unified cache system')
    return null
  }

  const result = getUnifiedCachedEmailDetail(userId, emailId)
  return result.success && result.data ? convertUnifiedToLegacyEmail(result.data) : null
}

/**
 * Set cached email detail
 * Updated to use unified cache system
 */
export function setCachedEmailDetail(email: CachedEmail, userId?: string): void {
  if (!userId) {
    console.warn('setCachedEmailDetail: userId is required for unified cache system')
    return
  }

  setUnifiedCachedEmailDetail(userId, convertLegacyToUnifiedEmail(email))
}

/**
 * Update cached email with partial data
 * Updated to use unified cache system
 */
export function updateCachedEmail(emailId: string, updates: Partial<CachedEmail>, userId?: string): void {
  if (!userId) {
    console.warn('updateCachedEmail: userId is required for unified cache system')
    return
  }

  // Convert partial legacy email to partial unified email
  const unifiedUpdates: Partial<UnifiedCachedEmail> = {
    ...updates,
    date: updates.date instanceof Date ? updates.date.toISOString() : updates.date ? String(updates.date) : undefined,
    to: updates.to || undefined,
    cc: updates.cc || undefined,
    bcc: updates.bcc || undefined,
    labels: updates.labels?.map((label: any) => label.id) || undefined,
    attachments: updates.attachments?.map((att: any) => ({
      filename: att.filename,
      mimeType: att.mimeType,
      size: att.size
    })) || undefined
  }

  updateUnifiedCachedEmail(userId, emailId, unifiedUpdates)
}

/**
 * Get cached thread by ID
 * Updated to use unified cache system
 */
export function getCachedThread(threadId: string, userId?: string): any {
  if (!userId) {
    console.warn('getCachedThread: userId is required for unified cache system')
    return null
  }

  const result = getUnifiedCachedThread(userId, threadId)
  return result.success && result.data ? result.data : null
}

/**
 * Set cached thread
 * Updated to use unified cache system
 */
export function setCachedThread(thread: any, userId?: string): void {
  if (!userId) {
    console.warn('setCachedThread: userId is required for unified cache system')
    return
  }

  setUnifiedCachedThread(userId, thread)
}

/**
 * Invalidate email cache
 * Updated to use unified cache system
 */
export function invalidateEmailCache(userId: string, query?: string): number {
  return invalidateUnifiedEmailCache(userId, query)
}

/**
 * Invalidate email detail cache
 * Updated to use unified cache system
 */
export function invalidateEmailDetail(userId: string, emailId: string): boolean {
  return invalidateUnifiedEmailDetail(userId, emailId)
}

/**
 * Invalidate thread cache
 * Updated to use unified cache system
 */
export function invalidateThread(userId: string, threadId: string): boolean {
  return invalidateUnifiedThread(userId, threadId)
}

/**
 * Cache multiple emails
 * Updated to use unified cache system
 */
export function cacheMultipleEmails(userId: string, emails: CachedEmail[]): any {
  const unifiedEmails = emails.map(convertLegacyToUnifiedEmail)
  return cacheUnifiedMultipleEmails(userId, unifiedEmails)
}

/**
 * Get multiple cached emails
 * Updated to use unified cache system
 */
export function getMultipleCachedEmails(userId: string, emailIds: string[]): CachedEmail[] {
  const result = getUnifiedMultipleCachedEmails(userId, emailIds)
  return result.success && result.data ? result.data.map(convertUnifiedToLegacyEmail) : []
}

/**
 * Preload Gmail data
 * Updated to use unified cache system
 */
export async function preloadGmailData(userId: string, options?: any): Promise<any> {
  return preloadUnifiedGmailData(userId, options)
}

/**
 * Get Gmail cache statistics
 * Updated to use unified cache system
 */
export function getGmailCacheStats(): any {
  return getUnifiedGmailCacheStats()
}

/**
 * Cleanup Gmail cache
 * Updated to use unified cache system
 */
export function cleanupGmailCache(userId?: string): number {
  return cleanupUnifiedGmailCache(userId)
}
