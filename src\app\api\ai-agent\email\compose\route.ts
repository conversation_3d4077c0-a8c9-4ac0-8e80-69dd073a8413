import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from "next-auth/next"
import { authOptions } from '@/lib/auth'
import { aiAgentEmailService } from '@/lib/ai/aiAgentEmail'

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions) as any
    
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const {
      to,
      cc,
      bcc,
      subject,
      context,
      tone = 'professional',
      purpose = 'general',
      keyPoints,
      sendImmediately = false,
      attachments
    } = await request.json()

    if (!to) {
      return NextResponse.json({ 
        error: 'Recipient (to) is required' 
      }, { status: 400 })
    }

    // Generate email content
    const emailContent = await aiAgentEmailService.composeEmail(session.user.id, {
      to,
      cc,
      bcc,
      subject,
      context,
      tone,
      purpose,
      keyPoints,
      attachments
    })

    // If sendImmediately is true, send the email
    if (sendImmediately) {
      const sendResult = await aiAgentEmailService.sendComposedEmail(session.user.id, {
        to: Array.isArray(to) ? to.join(', ') : to,
        subject: emailContent.subject,
        htmlBody: emailContent.htmlBody,
        textBody: emailContent.textBody,
        attachments
      })

      return NextResponse.json({
        emailContent,
        sendResult,
        success: true
      })
    }

    // Otherwise, just return the composed content
    return NextResponse.json({
      emailContent,
      success: true
    })

  } catch (error) {
    console.error('Email compose error:', error)
    return NextResponse.json(
      { error: 'Failed to compose email' },
      { status: 500 }
    )
  }
}
