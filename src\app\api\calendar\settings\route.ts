import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from "next-auth/next"
import { authOptions } from '@/lib/auth'
import { getCalendarClient } from '@/lib/unified-google-client'

// GET /api/calendar/settings - Get all calendar settings
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions) as any
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const settingId = searchParams.get('settingId')

    try {
      const { calendar } = await getCalendarClient(session.user.id)

      if (settingId) {
        // Get specific setting
        const response = await calendar.settings.get({
          setting: settingId
        })

        return NextResponse.json({
          setting: response.data,
          message: 'Setting retrieved successfully'
        })
      } else {
        // Get all settings
        const response = await calendar.settings.list()

        return NextResponse.json({
          settings: response.data.items || [],
          message: 'Settings retrieved successfully'
        })
      }

    } catch (error) {
      console.error('Google Calendar settings error:', error)
      return NextResponse.json(
        { error: 'Failed to fetch settings. Please reconnect your Google Calendar.' },
        { status: 403 }
      )
    }

  } catch (error) {
    console.error('Calendar settings error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch settings' },
      { status: 500 }
    )
  }
} 