import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from "next-auth/next"
import { authOptions } from '@/lib/auth'
import { getCalendarClient } from '@/lib/unified-google-client'
import { unifiedCacheService } from '@/lib/cache/unifiedCacheService'
import { generateCalendarSettingsCacheKey } from '@/lib/cache/utils/calendarConversion'

// GET /api/calendar/settings - Get all calendar settings
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions) as any
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const settingId = searchParams.get('settingId')

    // Try to get from cache first
    const cachedResult = unifiedCacheService.getCachedCalendarSettings(session.user.id)
    if (cachedResult.success && cachedResult.data && !settingId) {
      console.log('Calendar Settings API - Returning cached data')
      return NextResponse.json({
        settings: cachedResult.data,
        message: 'Settings retrieved successfully',
        cached: true
      })
    }

    try {
      const { calendar } = await getCalendarClient(session.user.id)

      if (settingId) {
        // Get specific setting
        const response = await calendar.settings.get({
          setting: settingId
        })

        return NextResponse.json({
          setting: response.data,
          message: 'Setting retrieved successfully'
        })
      } else {
        // Get all settings
        const response = await calendar.settings.list()
        const settings = response.data.items || []

        // Cache the settings
        if (settings.length > 0) {
          unifiedCacheService.cacheCalendarSettings(session.user.id, settings)
          console.log(`📅 Cached ${settings.length} calendar settings for user: ${session.user.id}`)
        }

        return NextResponse.json({
          settings: settings,
          message: 'Settings retrieved successfully'
        })
      }

    } catch (error) {
      console.error('Google Calendar settings error:', error)
      return NextResponse.json(
        { error: 'Failed to fetch settings. Please reconnect your Google Calendar.' },
        { status: 403 }
      )
    }

  } catch (error) {
    console.error('Calendar settings error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch settings' },
      { status: 500 }
    )
  }
} 