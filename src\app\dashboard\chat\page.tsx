'use client'

import { useState, useEffect } from 'react'
import { useSearchParams } from 'next/navigation'
import { ChatInterface } from '@/components/chat/chat-interface'
import { AIMessage } from '@/lib/ai/ai-agent'
import { useSession } from 'next-auth/react'
import { Loader2, AlertCircle } from 'lucide-react'
import { Alert, AlertDescription } from '@/components/ui/alert'

interface ConversationSummary {
  id: string
  title: string
  lastMessage?: {
    role: string
    content: string
    timestamp: Date
  }
  updatedAt: Date
  createdAt: Date
}

export default function ChatPage() {
  const { data: session, status } = useSession()
  const searchParams = useSearchParams()
  const sessionId = searchParams.get('session')
  
  const [currentConversationId, setCurrentConversationId] = useState<string | null>(sessionId)
  const [messages, setMessages] = useState<AIMessage[]>([])
  const [conversations, setConversations] = useState<ConversationSummary[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Load conversations on mount
  useEffect(() => {
    if (status === 'authenticated') {
      loadConversations()
    }
  }, [status])

  // Load messages when conversation changes
  useEffect(() => {
    if (currentConversationId && status === 'authenticated') {
      loadMessages(currentConversationId)
    } else {
      setMessages([])
    }
  }, [currentConversationId, status])

  const loadConversations = async () => {
    try {
      setError(null)
      const response = await fetch('/api/chat/conversations')
      const data = await response.json()
      
      if (response.ok && data.conversations) {
        // Transform the data to match our interface
        const transformedConversations = data.conversations.map((conv: any) => ({
          ...conv,
          updatedAt: new Date(conv.updatedAt),
          createdAt: new Date(conv.createdAt),
          lastMessage: conv.lastMessage ? {
            ...conv.lastMessage,
            timestamp: new Date(conv.lastMessage.timestamp)
          } : undefined
        }))
        setConversations(transformedConversations)
      } else {
        setError(data.error || 'Failed to load conversations')
      }
    } catch (error) {
      console.error('Error loading conversations:', error)
      setError('Failed to load conversations')
    }
  }

  const loadMessages = async (conversationId: string) => {
    try {
      setError(null)
      const response = await fetch(`/api/chat/conversations/${conversationId}/messages`)
      const data = await response.json()
      
      if (response.ok && data.messages) {
        // Transform the data to match our interface
        const transformedMessages = data.messages.map((msg: any) => ({
          ...msg,
          timestamp: new Date(msg.timestamp)
        }))
        setMessages(transformedMessages)
      } else {
        setError(data.error || 'Failed to load messages')
      }
    } catch (error) {
      console.error('Error loading messages:', error)
      setError('Failed to load messages')
    }
  }

  const handleSendMessage = async (content: string, conversationId?: string): Promise<AIMessage> => {
    setIsLoading(true)
    setError(null)
    
    try {
      let targetConversationId = conversationId || currentConversationId

      // Create new conversation if none exists
      if (!targetConversationId) {
        const createResponse = await fetch('/api/chat/conversations', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ title: 'New Chat' })
        })
        
        if (!createResponse.ok) {
          const errorData = await createResponse.json()
          throw new Error(errorData.error || 'Failed to create conversation')
        }
        
        const createData = await createResponse.json()
        if (createData.conversation) {
          targetConversationId = createData.conversation.id
          setCurrentConversationId(targetConversationId)
          await loadConversations() // Refresh conversations list
        }
      }

      // Send message
      const response = await fetch('/api/chat/message', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          conversationId: targetConversationId,
          content,
          context: {
            source: 'chat_interface',
            timestamp: new Date().toISOString()
          }
        })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to send message')
      }

      const data = await response.json()
      
      if (data.userMessage && data.aiMessage) {
        // Transform timestamps
        const userMessage = {
          ...data.userMessage,
          timestamp: new Date(data.userMessage.timestamp)
        }
        const aiMessage = {
          ...data.aiMessage,
          timestamp: new Date(data.aiMessage.timestamp)
        }
        
        // Update messages state
        setMessages(prev => [...prev, userMessage, aiMessage])
        
        // Update conversations list
        await loadConversations()
        
        return aiMessage
      }
      
      throw new Error('Invalid response format')
    } catch (error) {
      console.error('Error sending message:', error)
      const errorMessage = error instanceof Error ? error.message : 'Failed to send message'
      setError(errorMessage)
      throw error
    } finally {
      setIsLoading(false)
    }
  }

  const handleConversationSelect = (conversationId: string) => {
    setCurrentConversationId(conversationId)
    setError(null)
  }

  const handleNewConversation = async () => {
    try {
      setError(null)
      const response = await fetch('/api/chat/conversations', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ title: 'New Chat' })
      })
      
      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to create conversation')
      }
      
      const data = await response.json()
      
      if (data.conversation) {
        setCurrentConversationId(data.conversation.id)
        setMessages([])
        await loadConversations()
      }
    } catch (error) {
      console.error('Error creating conversation:', error)
      setError(error instanceof Error ? error.message : 'Failed to create conversation')
    }
  }

  // Show loading state while authenticating
  if (status === 'loading') {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="flex items-center gap-2">
          <Loader2 className="h-6 w-6 animate-spin" />
          <span>Loading...</span>
        </div>
      </div>
    )
  }

  // Show error if not authenticated
  if (status === 'unauthenticated') {
    return (
      <div className="h-full flex items-center justify-center">
        <Alert className="max-w-md">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Please sign in to access the chat interface.
          </AlertDescription>
        </Alert>
      </div>
    )
  }

  return (
    <div className="h-full flex flex-col">
      {/* Error Alert */}
      {error && (
        <Alert className="mx-4 mt-4 border-red-200 bg-red-50">
          <AlertCircle className="h-4 w-4 text-red-600" />
          <AlertDescription className="text-red-800">
            {error}
          </AlertDescription>
        </Alert>
      )}

      {/* Chat Interface */}
      <div className="flex-1 min-h-0">
        <ChatInterface
          conversationId={currentConversationId || undefined}
          onSendMessage={handleSendMessage}
          messages={messages}
          isLoading={isLoading}
          conversations={conversations}
          onConversationSelect={handleConversationSelect}
          onNewConversation={handleNewConversation}
          className="h-full"
        />
      </div>
    </div>
  )
}
