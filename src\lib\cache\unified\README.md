# Unified Cache System

A centralized, high-performance caching system for the Eagle Mass Mailer application that provides consistent caching across all modules (Gmail, Calendar, Meet, AI Agent) with comprehensive type safety, performance monitoring, and automatic cleanup.

## Architecture Overview

The unified cache system consists of:

- **Core Cache Manager**: Singleton cache manager with module-specific configurations
- **Module-Specific Helpers**: Dedicated cache helpers for Gmail, Calendar, Meet, and AI Agent
- **React Context Integration**: React hooks and context for seamless UI integration
- **Type Safety**: Comprehensive TypeScript interfaces and Zod validation
- **Performance Monitoring**: Built-in cache statistics and hit/miss tracking
- **Automatic Cleanup**: Configurable TTL and cleanup intervals per module

## Key Features

### 🚀 Performance Optimized
- **Module-specific TTL**: Gmail (2min), Calendar (15-20min), Meet (10-15min), AI Agent (30min)
- **Automatic cleanup intervals**: Prevents memory leaks with configurable cleanup schedules
- **Cache hit/miss tracking**: Real-time performance monitoring and statistics
- **Memory usage monitoring**: Track cache memory consumption across all modules

### 🔒 Type Safe
- **Comprehensive TypeScript interfaces**: Full type safety for all cache operations
- **Zod validation integration**: Runtime validation for cached data integrity
- **Generic cache operations**: Type-safe get/set operations with proper return types

### 🎯 Module Specific
- **Gmail Cache**: Email lists, email details, threads, drafts with optimized 2-minute TTL
- **Calendar Cache**: Events, calendars, availability with 15-20 minute TTL
- **Meet Cache**: Spaces, conferences, participants with 10-15 minute TTL
- **AI Agent Cache**: Conversations, user behavior, knowledge graphs with 30-minute TTL

### 🔄 Unified API
- **Consistent interface**: Same API patterns across all modules
- **Bulk operations**: Cache multiple items efficiently
- **Pattern-based invalidation**: Invalidate cache entries by pattern matching
- **User-specific caching**: Isolated cache per user with proper cleanup

## Usage Examples

### Basic Cache Operations

```typescript
import { 
  getCachedEmails, 
  setCachedEmails, 
  getCachedCalendarEvents,
  setCachedCalendarEvents 
} from '@/lib/cache/unified'

// Gmail caching
const emails = getCachedEmails(userId, 'inbox', 'primary')
if (emails.success && emails.data) {
  console.log('Cache hit:', emails.data)
} else {
  // Fetch from API and cache
  const freshEmails = await fetchEmailsFromAPI()
  setCachedEmails(userId, freshEmails, 'inbox', 'primary')
}

// Calendar caching
const events = getCachedCalendarEvents(userId, '2024-01-01', '2024-01-31')
if (events.success && events.data) {
  console.log('Cached events:', events.data)
}
```

### React Integration

```typescript
import { useUnifiedCache, useGmailCache, useCalendarCache } from '@/contexts/UnifiedCacheContext'

function MyComponent() {
  const { stats, preloadUserData, invalidateUserCache } = useUnifiedCache()
  const gmailCache = useGmailCache(userId)
  const calendarCache = useCalendarCache(userId)

  // Preload all user data
  const handlePreload = async () => {
    await preloadUserData(userId, {
      gmail: true,
      calendar: true,
      meet: true,
      aiAgent: true
    })
  }

  // Invalidate specific module cache
  const handleInvalidateGmail = () => {
    gmailCache.invalidateEmails(userId, 'inbox')
  }

  return (
    <div>
      <p>Cache Hit Rate: {stats?.hitRate}%</p>
      <p>Total Entries: {stats?.totalEntries}</p>
      <button onClick={handlePreload}>Preload Data</button>
      <button onClick={handleInvalidateGmail}>Clear Gmail Cache</button>
    </div>
  )
}
```

### Provider Setup

```typescript
import { UnifiedCacheProvider } from '@/contexts/UnifiedCacheContext'

function App() {
  return (
    <UnifiedCacheProvider
      config={{
        gmail: { ttl: 2 * 60 * 1000, maxSize: 1000 },
        calendar: { ttl: 15 * 60 * 1000, maxSize: 500 },
        meet: { ttl: 10 * 60 * 1000, maxSize: 200 },
        aiAgent: { ttl: 30 * 60 * 1000, maxSize: 100 }
      }}
      autoPreload={true}
      userId={currentUserId}
    >
      <YourApp />
    </UnifiedCacheProvider>
  )
}
```

## Module-Specific APIs

### Gmail Cache
```typescript
// Email list operations
getCachedEmails(userId, query?, category?)
setCachedEmails(userId, emails, query?, category?)
isEmailCacheValid(userId, query?, category?)

// Email detail operations
getCachedEmailDetail(userId, emailId)
setCachedEmailDetail(userId, email)
updateCachedEmail(userId, emailId, updates)

// Thread operations
getCachedThread(userId, threadId)
setCachedThread(userId, thread)

// Bulk operations
cacheMultipleEmails(userId, emails)
getMultipleCachedEmails(userId, emailIds)
```

### Calendar Cache
```typescript
// Event operations
getCachedCalendarEvents(userId, timeMin?, timeMax?, calendarId?)
setCachedCalendarEvents(userId, events, timeMin?, timeMax?, calendarId?)
getCachedCalendarEvent(userId, eventId, calendarId?)
setCachedCalendarEvent(userId, event, calendarId?)

// Availability operations
getCachedAvailability(userId, timeMin, timeMax)
setCachedAvailability(userId, availability, timeMin, timeMax)

// Calendar list operations
getCachedCalendarList(userId)
setCachedCalendarList(userId, calendars)
```

### Meet Cache
```typescript
// Space operations
getCachedMeetSpace(userId, spaceId)
setCachedMeetSpace(userId, space)
getCachedMeetSpaceList(userId, filter?)
setCachedMeetSpaceList(userId, spaces, filter?)

// Conference operations
getCachedMeetConference(userId, conferenceId)
setCachedMeetConference(userId, conference)
getCachedMeetConferenceList(userId, status?)
setCachedMeetConferenceList(userId, conferences, status?)

// Participants operations
getCachedMeetParticipants(userId, meetingId)
setCachedMeetParticipants(userId, participants, meetingId)
```

### AI Agent Cache
```typescript
// Conversation operations
getCachedConversation(userId, conversationId)
setCachedConversation(userId, conversation)
getCachedConversationList(userId, limit?, offset?)
setCachedConversationList(userId, conversations, limit?, offset?)

// User behavior operations
getCachedUserBehavior(userId)
setCachedUserBehavior(userId, behavior)

// Knowledge graph operations
getCachedKnowledgeGraph(userId, graphType?)
setCachedKnowledgeGraph(userId, knowledgeGraph, graphType?)

// AI context operations
getCachedAiContext(userId, contextType)
setCachedAiContext(userId, context, contextType)
```

## Cache Invalidation

### Pattern-Based Invalidation
```typescript
// Invalidate all emails for a user
invalidateEmailCache(userId)

// Invalidate specific email query
invalidateEmailCache(userId, 'inbox')

// Invalidate all calendar events
invalidateCalendarCache(userId)

// Invalidate specific calendar
invalidateCalendarCache(userId, 'primary')
```

### User-Specific Invalidation
```typescript
// Invalidate all cache for a user across all modules
const results = invalidateAllUserCache(userId)
console.log('Invalidated entries:', results)
// { gmail: 45, calendar: 12, meet: 8, aiAgent: 3 }
```

### Force Cleanup
```typescript
// Cleanup expired entries only
const cleaned = cleanupAllExpiredCache()

// Clear all cache (use with caution)
clearAllCache()
```

## Performance Monitoring

### Cache Statistics
```typescript
const stats = getAllCacheStats()
console.log('Global stats:', stats)
/*
{
  hitRate: 85.2,
  missRate: 14.8,
  totalEntries: 1247,
  memoryUsage: 2.4,
  moduleStats: {
    gmail: { hits: 342, misses: 58, entries: 400 },
    calendar: { hits: 156, misses: 24, entries: 180 },
    meet: { hits: 89, misses: 11, entries: 100 },
    aiAgent: { hits: 67, misses: 8, entries: 75 }
  }
}
*/
```

### Module-Specific Stats
```typescript
const gmailStats = getGmailCacheStats()
const calendarStats = getCalendarCacheStats()
const meetStats = getMeetCacheStats()
const aiAgentStats = getAiAgentCacheStats()
```

## Configuration

### Default Configuration
```typescript
const DEFAULT_CACHE_CONFIG = {
  gmail: { 
    ttl: 2 * 60 * 1000,        // 2 minutes
    maxSize: 1000,             // Max 1000 entries
    cleanupInterval: 5 * 60 * 1000  // Cleanup every 5 minutes
  },
  calendar: { 
    ttl: 15 * 60 * 1000,       // 15 minutes
    maxSize: 500,              // Max 500 entries
    cleanupInterval: 10 * 60 * 1000 // Cleanup every 10 minutes
  },
  meet: { 
    ttl: 10 * 60 * 1000,       // 10 minutes
    maxSize: 200,              // Max 200 entries
    cleanupInterval: 15 * 60 * 1000 // Cleanup every 15 minutes
  },
  aiAgent: { 
    ttl: 30 * 60 * 1000,       // 30 minutes
    maxSize: 100,              // Max 100 entries
    cleanupInterval: 20 * 60 * 1000 // Cleanup every 20 minutes
  }
}
```

### Custom Configuration
```typescript
import { initializeUnifiedCache } from '@/lib/cache/unified'

initializeUnifiedCache({
  gmail: { ttl: 5 * 60 * 1000 }, // 5 minutes for Gmail
  calendar: { ttl: 30 * 60 * 1000 } // 30 minutes for Calendar
})
```

## Migration from Legacy Cache

The unified cache system maintains backward compatibility with existing cache usage patterns:

```typescript
// Legacy usage (still works)
import { getCachedEmails, setCachedEmails } from '@/lib/cache/gmail/gmailCacheHelper'

// New unified usage (recommended)
import { getCachedEmails, setCachedEmails } from '@/lib/cache/unified'
```

## Best Practices

1. **Always provide userId**: All cache operations require a userId for proper isolation
2. **Use appropriate TTL**: Different data types have different freshness requirements
3. **Monitor cache statistics**: Regular monitoring helps optimize cache performance
4. **Implement preloading**: Preload frequently accessed data for better UX
5. **Handle cache misses gracefully**: Always have fallback logic for cache misses
6. **Use bulk operations**: When caching multiple items, use bulk operations for efficiency
7. **Invalidate strategically**: Use pattern-based invalidation to clear related cache entries

## Troubleshooting

### Common Issues

1. **Cache not working**: Ensure userId is provided and cache is properly initialized
2. **Memory issues**: Monitor cache size and adjust maxSize configuration
3. **Stale data**: Check TTL settings and implement proper invalidation strategies
4. **Performance issues**: Monitor hit rates and optimize cache keys and patterns

### Debug Mode
```typescript
// Enable debug logging
localStorage.setItem('cache-debug', 'true')

// View cache contents
console.log(cacheManager.getStats())
```
