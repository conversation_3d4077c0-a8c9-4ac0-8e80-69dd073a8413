import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from "next-auth/next"
import { authOptions } from '@/lib/auth'
import { getCalendarClient } from '@/lib/unified-google-client'
import { unifiedCacheService } from '@/lib/cache/unifiedCacheService'

// GET /api/calendar/colors - Get color definitions for calendars and events
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions) as any
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Try to get from cache first
    const cachedResult = unifiedCacheService.getCachedCalendarColors(session.user.id)
    if (cachedResult.success && cachedResult.data) {
      console.log('Calendar Colors API - Returning cached data')
      return NextResponse.json({
        colors: cachedResult.data,
        message: 'Colors retrieved successfully',
        cached: true
      })
    }

    try {
      const { calendar } = await getCalendarClient(session.user.id)

      const response = await calendar.colors.get()

      // Cache the colors
      if (response.data) {
        unifiedCacheService.cacheCalendarColors(session.user.id, response.data)
        console.log(`📅 Cached calendar colors for user: ${session.user.id}`)
      }

      return NextResponse.json({
        colors: response.data,
        message: 'Colors retrieved successfully'
      })

    } catch (error) {
      console.error('Google Calendar colors error:', error)
      return NextResponse.json(
        { error: 'Failed to fetch colors. Please reconnect your Google Calendar.' },
        { status: 403 }
      )
    }

  } catch (error) {
    console.error('Calendar colors error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch colors' },
      { status: 500 }
    )
  }
} 