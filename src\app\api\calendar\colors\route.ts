import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from "next-auth/next"
import { authOptions } from '@/lib/auth'
import { getCalendarClient } from '@/lib/unified-google-client'

// GET /api/calendar/colors - Get color definitions for calendars and events
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions) as any
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    try {
      const { calendar } = await getCalendarClient(session.user.id)

      const response = await calendar.colors.get()

      return NextResponse.json({
        colors: response.data,
        message: 'Colors retrieved successfully'
      })

    } catch (error) {
      console.error('Google Calendar colors error:', error)
      return NextResponse.json(
        { error: 'Failed to fetch colors. Please reconnect your Google Calendar.' },
        { status: 403 }
      )
    }

  } catch (error) {
    console.error('Calendar colors error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch colors' },
      { status: 500 }
    )
  }
} 