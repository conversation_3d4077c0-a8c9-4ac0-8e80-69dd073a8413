/**
 * Application Preloader - Comprehensive Data Preloading System
 * Preloads all critical data when user first hits the app for instant navigation
 */

import { unifiedCacheService } from '@/lib/cache/unifiedCacheService'

export interface PreloadOptions {
  userId: string
  priority?: 'high' | 'medium' | 'low'
  modules?: {
    gmail?: boolean
    calendar?: boolean
    meet?: boolean
    aiAgent?: boolean
  }
  timeRanges?: {
    calendar?: {
      pastDays?: number
      futureDays?: number
    }
    gmail?: {
      maxEmails?: number
      includeDrafts?: boolean
      includeSent?: boolean
    }
  }
}

export interface PreloadResult {
  success: boolean
  module: string
  dataType: string
  cached: boolean
  loadTime: number
  error?: string
}

export interface PreloadSummary {
  totalTime: number
  successCount: number
  errorCount: number
  results: PreloadResult[]
  cacheHitRate: number
}

class AppPreloader {
  private isPreloading = false
  private preloadQueue: Map<string, Promise<PreloadResult[]>> = new Map()

  /**
   * Main preload function - loads all critical app data
   */
  async preloadAllAppData(options: PreloadOptions): Promise<PreloadSummary> {
    const startTime = Date.now()
    const { userId, modules = {}, priority = 'high' } = options

    // Prevent duplicate preloading for same user
    const cacheKey = `preload:${userId}`
    if (this.preloadQueue.has(cacheKey)) {
      console.log('🔄 Preloading already in progress for user:', userId)
      return await this.preloadQueue.get(cacheKey)!.then(() => ({ 
        totalTime: 0, 
        successCount: 0, 
        errorCount: 0, 
        results: [], 
        cacheHitRate: 0 
      }))
    }

    this.isPreloading = true
    console.log('🚀 Starting comprehensive app preloading for user:', userId)

    const preloadPromise = this.executePreloading(options)
    this.preloadQueue.set(cacheKey, preloadPromise)

    try {
      const results = await preloadPromise
      const totalTime = Date.now() - startTime
      
      const summary: PreloadSummary = {
        totalTime,
        successCount: results.filter(r => r.success).length,
        errorCount: results.filter(r => !r.success).length,
        results,
        cacheHitRate: results.filter(r => r.cached).length / results.length
      }

      console.log('✅ App preloading completed:', {
        totalTime: `${totalTime}ms`,
        success: summary.successCount,
        errors: summary.errorCount,
        cacheHitRate: `${(summary.cacheHitRate * 100).toFixed(1)}%`
      })

      return summary
    } finally {
      this.isPreloading = false
      this.preloadQueue.delete(cacheKey)
    }
  }

  /**
   * Execute preloading based on priority
   */
  private async executePreloading(options: PreloadOptions): Promise<PreloadResult[]> {
    const { priority = 'high', modules = {} } = options
    const results: PreloadResult[] = []

    // Define preloading strategies based on priority
    const strategies = {
      high: {
        // Critical data - load immediately and in parallel
        immediate: ['gmail:inbox', 'calendar:today', 'calendar:thisWeek'],
        parallel: ['gmail:sent', 'gmail:drafts', 'calendar:thisMonth', 'meet:recent'],
        background: ['aiAgent:conversations', 'calendar:colors', 'gmail:labels']
      },
      medium: {
        immediate: ['gmail:inbox', 'calendar:today'],
        parallel: ['calendar:thisWeek', 'meet:recent'],
        background: ['gmail:sent', 'gmail:drafts', 'calendar:thisMonth']
      },
      low: {
        immediate: ['gmail:inbox'],
        parallel: ['calendar:today'],
        background: ['calendar:thisWeek']
      }
    }

    const strategy = strategies[priority]

    // Phase 1: Critical immediate data
    console.log('📋 Phase 1: Loading critical immediate data')
    const immediateResults = await this.loadDataItems(options, strategy.immediate)
    results.push(...immediateResults)

    // Phase 2: Important parallel data
    console.log('📋 Phase 2: Loading important data in parallel')
    const parallelResults = await this.loadDataItemsParallel(options, strategy.parallel)
    results.push(...parallelResults)

    // Phase 3: Background data (don't wait for completion)
    console.log('📋 Phase 3: Starting background data loading')
    this.loadDataItemsBackground(options, strategy.background)

    return results
  }

  /**
   * Load data items sequentially (for critical data)
   */
  private async loadDataItems(options: PreloadOptions, items: string[]): Promise<PreloadResult[]> {
    const results: PreloadResult[] = []
    
    for (const item of items) {
      const result = await this.loadSingleDataItem(options, item)
      results.push(result)
      
      // Short delay between critical items to prevent overwhelming
      await new Promise(resolve => setTimeout(resolve, 50))
    }
    
    return results
  }

  /**
   * Load data items in parallel (for important but non-critical data)
   */
  private async loadDataItemsParallel(options: PreloadOptions, items: string[]): Promise<PreloadResult[]> {
    const promises = items.map(item => this.loadSingleDataItem(options, item))
    return await Promise.allSettled(promises).then(results => 
      results.map(result => 
        result.status === 'fulfilled' ? result.value : {
          success: false,
          module: 'unknown',
          dataType: 'unknown',
          cached: false,
          loadTime: 0,
          error: 'Promise rejected'
        }
      )
    )
  }

  /**
   * Load data items in background (fire and forget)
   */
  private loadDataItemsBackground(options: PreloadOptions, items: string[]): void {
    items.forEach(item => {
      this.loadSingleDataItem(options, item).catch(error => {
        console.warn(`Background preload failed for ${item}:`, error)
      })
    })
  }

  /**
   * Load a single data item
   */
  private async loadSingleDataItem(options: PreloadOptions, item: string): Promise<PreloadResult> {
    const startTime = Date.now()
    const [module, dataType] = item.split(':')
    const { userId, timeRanges = {} } = options

    try {
      // Check if data is already cached
      const cacheKey = this.generateCacheKey(module, dataType, options)
      const cachedData = unifiedCacheService.getCachedData(module as any, cacheKey, userId)
      
      if (cachedData.success && cachedData.data) {
        return {
          success: true,
          module,
          dataType,
          cached: true,
          loadTime: Date.now() - startTime
        }
      }

      // Load fresh data based on module and type
      const freshData = await this.fetchFreshData(module, dataType, options)
      
      // Cache the fresh data
      if (freshData) {
        unifiedCacheService.cacheData(module as any, cacheKey, freshData, userId)
      }

      return {
        success: true,
        module,
        dataType,
        cached: false,
        loadTime: Date.now() - startTime
      }
    } catch (error) {
      return {
        success: false,
        module,
        dataType,
        cached: false,
        loadTime: Date.now() - startTime,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * Generate cache key for data item
   */
  private generateCacheKey(module: string, dataType: string, options: PreloadOptions): string {
    const { timeRanges = {} } = options
    
    switch (`${module}:${dataType}`) {
      case 'gmail:inbox':
        return `emails:inbox:${timeRanges.gmail?.maxEmails || 50}`
      case 'gmail:sent':
        return `emails:sent:${timeRanges.gmail?.maxEmails || 25}`
      case 'gmail:drafts':
        return 'emails:drafts'
      case 'gmail:labels':
        return 'labels:all'
      case 'calendar:today':
        const today = new Date().toISOString().split('T')[0]
        return `events:${today}:${today}`
      case 'calendar:thisWeek':
        const weekStart = new Date()
        const weekEnd = new Date(weekStart.getTime() + 7 * 24 * 60 * 60 * 1000)
        return `events:${weekStart.toISOString()}:${weekEnd.toISOString()}`
      case 'calendar:thisMonth':
        const monthStart = new Date()
        monthStart.setDate(1)
        const monthEnd = new Date(monthStart.getFullYear(), monthStart.getMonth() + 1, 0)
        return `events:${monthStart.toISOString()}:${monthEnd.toISOString()}`
      case 'calendar:colors':
        return 'colors:all'
      case 'meet:recent':
        return 'conferences:recent'
      case 'aiAgent:conversations':
        return 'conversations:recent'
      default:
        return `${dataType}:default`
    }
  }

  /**
   * Fetch fresh data from APIs
   */
  private async fetchFreshData(module: string, dataType: string, options: PreloadOptions): Promise<any> {
    const { timeRanges = {} } = options

    try {
      switch (`${module}:${dataType}`) {
        case 'gmail:inbox':
          const inboxResponse = await fetch(`/api/gmail/inbox?maxResults=${timeRanges.gmail?.maxEmails || 50}`)
          return inboxResponse.ok ? (await inboxResponse.json()).emails : null

        case 'gmail:sent':
          const sentResponse = await fetch(`/api/gmail/sent?maxResults=${timeRanges.gmail?.maxEmails || 25}`)
          return sentResponse.ok ? (await sentResponse.json()).emails : null

        case 'gmail:drafts':
          const draftsResponse = await fetch('/api/gmail/drafts')
          return draftsResponse.ok ? (await draftsResponse.json()).drafts : null

        case 'gmail:labels':
          const labelsResponse = await fetch('/api/gmail/labels')
          return labelsResponse.ok ? (await labelsResponse.json()).labels : null

        case 'calendar:today':
          const today = new Date()
          const todayEnd = new Date(today)
          todayEnd.setHours(23, 59, 59, 999)
          const todayResponse = await fetch(`/api/calendar?timeMin=${today.toISOString()}&timeMax=${todayEnd.toISOString()}`)
          return todayResponse.ok ? (await todayResponse.json()).events : null

        case 'calendar:thisWeek':
          const weekStart = new Date()
          const weekEnd = new Date(weekStart.getTime() + 7 * 24 * 60 * 60 * 1000)
          const weekResponse = await fetch(`/api/calendar?timeMin=${weekStart.toISOString()}&timeMax=${weekEnd.toISOString()}`)
          return weekResponse.ok ? (await weekResponse.json()).events : null

        case 'calendar:thisMonth':
          const monthStart = new Date()
          monthStart.setDate(1)
          const monthEnd = new Date(monthStart.getFullYear(), monthStart.getMonth() + 1, 0)
          const monthResponse = await fetch(`/api/calendar?timeMin=${monthStart.toISOString()}&timeMax=${monthEnd.toISOString()}`)
          return monthResponse.ok ? (await monthResponse.json()).events : null

        case 'calendar:colors':
          const colorsResponse = await fetch('/api/calendar/colors')
          return colorsResponse.ok ? (await colorsResponse.json()).colors : null

        case 'meet:recent':
          const meetResponse = await fetch('/api/meet/conferences')
          return meetResponse.ok ? (await meetResponse.json()).conferences : null

        case 'aiAgent:conversations':
          const conversationsResponse = await fetch('/api/ai/conversations')
          return conversationsResponse.ok ? (await conversationsResponse.json()).conversations : null

        default:
          console.warn(`Unknown data type for preloading: ${module}:${dataType}`)
          return null
      }
    } catch (error) {
      console.error(`Error fetching ${module}:${dataType}:`, error)
      return null
    }
  }

  /**
   * Check if preloading is currently in progress
   */
  get isCurrentlyPreloading(): boolean {
    return this.isPreloading
  }

  /**
   * Get preloading progress for a user
   */
  getPreloadingProgress(userId: string): { inProgress: boolean; queueSize: number } {
    return {
      inProgress: this.preloadQueue.has(`preload:${userId}`),
      queueSize: this.preloadQueue.size
    }
  }
}

// Export singleton instance
export const appPreloader = new AppPreloader()

// Export convenience functions
export const preloadAllAppData = (options: PreloadOptions) => appPreloader.preloadAllAppData(options)
export const isPreloading = () => appPreloader.isCurrentlyPreloading
export const getPreloadingProgress = (userId: string) => appPreloader.getPreloadingProgress(userId)
