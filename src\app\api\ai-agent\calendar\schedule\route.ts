import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from "next-auth/next"
import { authOptions } from '@/lib/auth'
import { aiAgentCalendarService } from '@/lib/ai/aiAgentCalendar'
import { startPerformanceMetric, endPerformanceMetric } from '@/lib/performance/monitor'

export async function POST(request: NextRequest) {
  try {
    // Start performance monitoring
    startPerformanceMetric('ai-agent-calendar-schedule', { endpoint: '/api/ai-agent/calendar/schedule' })

    const session = await getServerSession(authOptions) as any

    if (!session?.user?.email) {
      endPerformanceMetric('ai-agent-calendar-schedule')
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const {
      context,
      attendees,
      preferredDate,
      preferredTime,
      duration = 60,
      timezone = 'UTC',
      meetingType = 'virtual',
      location,
      addMeetLink = true
    } = await request.json()

    if (!context) {
      endPerformanceMetric('ai-agent-calendar-schedule')
      return NextResponse.json({
        error: 'Meeting context is required'
      }, { status: 400 })
    }

    const result = await aiAgentCalendarService.parseAndScheduleMeeting(session.user.id, {
      context,
      attendees,
      preferredDate,
      preferredTime,
      duration,
      timezone,
      meetingType,
      location,
      addMeetLink
    })

    // End performance monitoring
    const performanceDuration = endPerformanceMetric('ai-agent-calendar-schedule')
    if (performanceDuration && performanceDuration > 3000) {
      console.warn(`Slow AI calendar schedule: ${performanceDuration.toFixed(2)}ms for user ${session.user.id}`)
    }

    return NextResponse.json({
      result,
      success: true
    })

  } catch (error) {
    endPerformanceMetric('ai-agent-calendar-schedule')
    console.error('Meeting scheduling error:', error)
    return NextResponse.json(
      { error: 'Failed to schedule meeting' },
      { status: 500 }
    )
  }
}
