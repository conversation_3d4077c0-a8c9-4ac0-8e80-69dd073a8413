import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from "next-auth/next"
import { authOptions } from '@/lib/auth'
import { aiAgentTaskService } from '@/lib/ai/aiAgentTasks'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions) as any
    
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const days = parseInt(searchParams.get('days') || '30')

    const result = await aiAgentTaskService.getTaskStatistics(session.user.id, days)

    return NextResponse.json({
      ...result,
      success: true
    })

  } catch (error) {
    console.error('Task statistics error:', error)
    return NextResponse.json(
      { error: 'Failed to get task statistics' },
      { status: 500 }
    )
  }
}
