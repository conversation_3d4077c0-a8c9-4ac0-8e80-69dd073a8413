import { GoogleGenerativeA<PERSON> } from '@google/generative-ai'
import { modernEncryption } from '../encryption'
import { prisma } from '../prisma'
import { format } from 'date-fns'

const genAI = new GoogleGenerativeAI(process.env.GEMINI_API || "AIzaSyAwHr4qa8SLtb7RMqaSRFzgAo6YwpP2Ga0")
const model = genAI.getGenerativeModel({ model: process.env.GEMINI_MODEL || "gemini-2.5-flash-lite-preview-06-17" })

export interface AIMessage {
  id: string
  role: 'user' | 'assistant' | 'system'
  content: string
  timestamp: Date
  metadata?: {
    model?: string
    tokens?: number
    context?: any
    actions?: AIAction[]
  }
}

export interface AIAction {
  type: 'send_email' | 'schedule_meeting' | 'create_task' | 'add_calendar_event' | 'analyze_emails'
  data: any
  status: 'pending' | 'completed' | 'failed'
  result?: any
}

export interface EmailSummary {
  id: string
  subject: string
  from: string
  date: Date
  importance: 'high' | 'medium' | 'low'
  summary: string
  category: string
  actionItems: string[]
  sentiment: 'positive' | 'neutral' | 'negative'
}

export interface MeetingRequest {
  title: string
  description: string
  attendees: string[]
  startTime: Date
  endTime: Date
  location?: string
  meetLink?: boolean
}

export interface TaskRequest {
  title: string
  description: string
  dueDate?: Date
  priority: 'high' | 'medium' | 'low'
  type: 'email' | 'meeting' | 'reminder' | 'follow_up' | 'bill_payment' | 'deadline'
  relatedEmails?: string[]
  relatedContacts?: string[]
}

class AIAgentService {
  // Use unified cache system
  private static readonly CACHE_DURATION = 30 * 60 * 1000 // 30 minutes for AI Agent data

  private getCachedData<T>(userId: string, type: string): T | null {
    try {
      // Import unified cache service dynamically to avoid circular dependencies
      const { unifiedCacheService } = require('@/lib/cache/unifiedCacheService')
      const result = (unifiedCacheService as any).getCachedData('aiAgent', type, userId)

      if (result.success && result.data) {
        console.log(`📦 Using cached AI data for ${type}`)
        return result.data as T
      }

      return null
    } catch (error) {
      console.warn('Failed to get cached data:', error)
      return null
    }
  }

  private setCachedData(userId: string, type: string, data: any): void {
    try {
      // Import unified cache service dynamically to avoid circular dependencies
      const { unifiedCacheService } = require('@/lib/cache/unifiedCacheService')
      const result = unifiedCacheService.cacheData('aiAgent', type, data, userId)

      if (result.success) {
        console.log(`💾 Cached AI data for ${type}`)
      }
    } catch (error) {
      console.warn('Failed to set cached data:', error)
    }
  }

  private async encryptData(data: string, userId: string, resourceId: string): Promise<{ encryptedData: string; salt: string }> {
    const salt = modernEncryption.generateEncryptionSalt(userId, resourceId, 'chat')
    const encryptedData = await modernEncryption.encryptData(data, userId, 'chat', resourceId)
    return { encryptedData, salt }
  }

  private async decryptData(encryptedData: string, userId: string, resourceId: string): Promise<string> {
    try {
      // Try new encryption method first
      return await modernEncryption.decryptData(encryptedData, userId, 'chat', resourceId)
    } catch (error) {
      // If that fails, try to handle as plain text (for backwards compatibility)
      console.warn('Decryption failed, treating as plain text:', error)
      return encryptedData
    }
  }

  async createConversation(userId: string, title: string = 'New Chat'): Promise<string> {
    const conversationId = `conv_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    const { encryptedData: encryptedTitle, salt } = await this.encryptData(title, userId, conversationId)

    const conversation = await (prisma as any).conversation.create({
      data: {
        id: conversationId,
        userId,
        title: encryptedTitle,
        encryptionSalt: salt,
        status: 'active'
      }
    })

    return conversation.id
  }

  async sendMessage(
    conversationId: string, 
    userId: string, 
    content: string, 
    context?: any
  ): Promise<AIMessage> {
    // Get conversation and enhanced context
    const conversation = await (prisma as any).conversation.findFirst({
      where: { id: conversationId, userId }
    })

    if (!conversation) {
      throw new Error('Conversation not found')
    }

    // Get advanced conversation context using the new method
    const conversationHistory = await this.getConversationContext(conversationId, 15)

    // Get user behavior and knowledge for personalization
    const userBehavior = await this.getUserBehavior(userId)
    const knowledgeEntries = await this.getRelevantKnowledge(userId, content)

    // Track user behavior
    await this.trackUserBehavior(userId, 'chat_message', {
      conversationId,
      messageLength: content.length,
      timestamp: new Date()
    })

    // Store user message
    const userMessageId = `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    const { encryptedData: encryptedUserContent, salt: userMessageSalt } = await this.encryptData(content, userId, userMessageId)

    const userMessage = await (prisma as any).message.create({
      data: {
        id: userMessageId,
        conversationId,
        role: 'user',
        content: encryptedUserContent,
        encryptionSalt: userMessageSalt
      }
    })

    // Generate AI response with advanced context
    const aiResponse = await this.generateResponse(
      content,
      conversationHistory,
      context,
      userId,
      userBehavior,
      knowledgeEntries
    )

    // Store AI message
    const aiMessageId = `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    const { encryptedData: encryptedAIContent, salt: aiMessageSalt } = await this.encryptData(aiResponse.content, userId, aiMessageId)
    const encryptedMetadata = aiResponse.metadata 
      ? (await this.encryptData(JSON.stringify(aiResponse.metadata), userId, aiMessageId)).encryptedData
      : null

    const aiMessage = await (prisma as any).message.create({
      data: {
        id: aiMessageId,
        conversationId,
        role: 'assistant',
        content: encryptedAIContent,
        metadata: encryptedMetadata,
        encryptionSalt: aiMessageSalt
      }
    })

    // Update conversation timestamp
    await (prisma as any).conversation.update({
      where: { id: conversationId },
      data: { updatedAt: new Date() }
    })

    // Execute any actions if present
    if (aiResponse.metadata?.actions) {
      await this.executeActions(aiResponse.metadata.actions, userId, conversationId)
    }

    // Update knowledge graph based on conversation
    await this.updateKnowledgeGraph(userId, content, aiResponse.content)

    return {
      id: aiMessage.id,
      role: 'assistant',
      content: aiResponse.content,
      timestamp: aiMessage.createdAt,
      metadata: aiResponse.metadata
    }
  }

  private async generateResponse(
    userMessage: string,
    conversationHistory: string,
    context: any,
    userId: string,
    userBehavior?: any,
    knowledgeEntries?: any[]
  ): Promise<AIMessage> {
    // Use provided user behavior and knowledge entries, or fetch if not provided
    const behaviorData = userBehavior || await this.getUserBehavior(userId)
    const knowledgeData = knowledgeEntries || await this.getRelevantKnowledge(userId, userMessage)

    // Analyze user intent
    const intent = await this.analyzeIntent(userMessage)

    // COMPREHENSIVE DATA FETCHING - Pull all relevant data automatically
    const comprehensiveData = await this.fetchComprehensiveUserData(userId, intent, userMessage)

    // Build comprehensive prompt with enhanced context and all data
    const prompt = this.buildAgentPrompt(
      userMessage,
      conversationHistory,
      context,
      behaviorData,
      knowledgeData,
      intent,
      comprehensiveData
    )

    try {
      const result = await model.generateContent(prompt)
      const response = await result.response
      let responseText = response.text()

      // Parse any structured actions from the response
      const actions = await this.parseActionsFromResponse(responseText, intent, userMessage)

      // Format response for better presentation
      responseText = this.formatResponse(responseText)

      return {
        id: `ai-${Date.now()}`,
        role: 'assistant',
        content: responseText,
        timestamp: new Date(),
        metadata: {
          model: 'gemini-2.5-flash-lite-preview-06-17',
          context: context,
          actions: actions
        }
      }
    } catch (error) {
      console.error('Error generating AI response:', error)
      throw error
    }
  }

  private async analyzeIntent(message: string): Promise<string> {
    const intentPrompt = `
    Analyze the user's intent from this message and classify it into one of these categories:
    - email_analysis: User wants to analyze, summarize, or get insights from emails
    - email_compose: User wants to send an email or draft an email
    - meeting_schedule: User wants to schedule a meeting or calendar event
    - task_create: User wants to create a task, reminder, or add something to calendar (includes phrases like "add to calendar", "remind me", "create task", "Sample Reminder")
    - information_query: User is asking for information or clarification
    - casual_chat: General conversation or greeting
    - system_help: User needs help with the system or features

    IMPORTANT: If the user mentions adding something to calendar, creating reminders, or scheduling tasks, always classify as "task_create".

    Message: "${message}"

    Respond with just the category name.
    `

    try {
      const result = await model.generateContent(intentPrompt)
      const response = await result.response
      return response.text().trim().toLowerCase()
    } catch (error) {
      console.error('Error analyzing intent:', error)
      return 'information_query'
    }
  }

  private async fetchComprehensiveUserData(userId: string, intent: string, userMessage: string): Promise<any> {
    // Check cache first
    const cachedData = this.getCachedData<any>(userId, 'comprehensive')
    if (cachedData) {
      console.log('📦 Using cached comprehensive data')
      return cachedData
    }

    const data: any = {
      emails: {},
      calendar: {},
      tasks: {},
      insights: {},
      timestamp: new Date()
    }

    try {
      // Check individual cache entries first
      const cachedEmails = this.getCachedData<any>(userId, 'emails')
      const cachedCalendar = this.getCachedData<any>(userId, 'calendar')
      const cachedTasks = this.getCachedData<any>(userId, 'tasks')
      const cachedInsights = this.getCachedData<any>(userId, 'insights')

      // Only fetch data that's not cached
      const emailPromises = []
      const calendarPromises = []
      const otherPromises = []

      if (!cachedEmails) {
        emailPromises.push(
          this.fetchUserEmails(userId, 'inbox', 20),
          this.fetchUserEmails(userId, 'sent', 10),
          this.fetchUserEmails(userId, 'unread', 15)
        )
      }

      if (!cachedCalendar) {
        calendarPromises.push(
          this.fetchUserCalendar(userId),
          this.fetchUserMeetings(userId)
        )
      }

      if (!cachedTasks) {
        otherPromises.push(this.fetchUserTasks(userId))
      }

      if (!cachedInsights) {
        otherPromises.push(this.fetchEmailInsights(userId))
      }

      // Execute all data fetching in parallel for speed
      const [emailResults, calendarResults, otherResults] = await Promise.allSettled([
        emailPromises.length > 0 ? Promise.allSettled(emailPromises) : Promise.resolve([]),
        calendarPromises.length > 0 ? Promise.allSettled(calendarPromises) : Promise.resolve([]),
        otherPromises.length > 0 ? Promise.allSettled(otherPromises) : Promise.resolve([])
      ])

      // Use cached data or process fresh results
      if (cachedEmails) {
        data.emails = cachedEmails
      } else if (emailResults.status === 'fulfilled' && Array.isArray(emailResults.value)) {
        const [inboxResult, sentResult, unreadResult] = emailResults.value
        if (inboxResult?.status === 'fulfilled') data.emails.inbox = inboxResult.value
        if (sentResult?.status === 'fulfilled') data.emails.sent = sentResult.value
        if (unreadResult?.status === 'fulfilled') data.emails.unread = unreadResult.value
        this.setCachedData(userId, 'emails', data.emails)
      }

      if (cachedCalendar) {
        data.calendar = cachedCalendar
      } else if (calendarResults.status === 'fulfilled' && Array.isArray(calendarResults.value)) {
        const [calendarResult, meetingsResult] = calendarResults.value
        if (calendarResult?.status === 'fulfilled') data.calendar.events = calendarResult.value
        if (meetingsResult?.status === 'fulfilled') data.calendar.meetings = meetingsResult.value
        this.setCachedData(userId, 'calendar', data.calendar)
      }

      if (cachedTasks) {
        data.tasks = cachedTasks
      } else if (otherResults.status === 'fulfilled' && Array.isArray(otherResults.value)) {
        const tasksResult = otherResults.value.find((r: any) => r.value?.tasks)
        if (tasksResult?.status === 'fulfilled') {
          data.tasks = tasksResult.value
          this.setCachedData(userId, 'tasks', data.tasks)
        }
      }

      if (cachedInsights) {
        data.insights = cachedInsights
      } else if (otherResults.status === 'fulfilled' && Array.isArray(otherResults.value)) {
        const insightsResult = otherResults.value.find((r: any) => r.value?.insights)
        if (insightsResult?.status === 'fulfilled') {
          data.insights = insightsResult.value
          this.setCachedData(userId, 'insights', data.insights)
        }
      }

      // Add summary counts for quick reference
      data.summary = {
        totalInboxEmails: data.emails.inbox?.emails?.length || 0,
        unreadEmailsCount: data.emails.unread?.emails?.length || 0,
        upcomingEventsCount: data.calendar.events?.events?.length || 0,
        pendingTasksCount: data.tasks?.tasks?.filter((t: any) => t.status !== 'completed')?.length || 0
      }

      // Cache the comprehensive data
      this.setCachedData(userId, 'comprehensive', data)

      return data
    } catch (error) {
      console.error('Error fetching comprehensive user data:', error)
      return data // Return partial data even if some fetches fail
    }
  }

  private async fetchUserEmails(userId: string, category: string, limit: number = 20): Promise<any> {
    try {
      // Import Gmail functions dynamically to avoid circular dependencies
      const { getInboxEmails, getSentEmails, getDraftEmails } = await import('../gmail')

      switch (category) {
        case 'inbox':
          return await getInboxEmails(userId, limit)
        case 'sent':
          return await getSentEmails(userId, limit)
        case 'unread':
          // For unread, we'll get inbox emails and filter for unread ones
          const inboxResult = await getInboxEmails(userId, limit * 2) // Get more to filter
          if (inboxResult.emails) {
            const unreadEmails = inboxResult.emails.filter((email: any) => !email.read)
            return { emails: unreadEmails.slice(0, limit), totalCount: unreadEmails.length }
          }
          return inboxResult
        case 'drafts':
          return await getDraftEmails(userId, limit)
        default:
          return await getInboxEmails(userId, limit)
      }
    } catch (error) {
      console.error(`Error fetching ${category} emails:`, error)
      return { emails: [], error: error instanceof Error ? error.message : 'Unknown error' }
    }
  }

  private async fetchUserCalendar(userId: string): Promise<any> {
    try {
      // Calendar functionality temporarily disabled
      console.log('Calendar functionality temporarily disabled')
      return { events: [], count: 0 }
      
      // Use the calendar client directly
      // const { getCalendarClient } = await import('./unified-google-client')
      // const { calendar } = await getCalendarClient(userId)

      // const today = new Date()
      // const nextWeek = new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000)

      // const response = await calendar.events.list({
      //   calendarId: 'primary',
      //   timeMin: today.toISOString(),
      //   timeMax: nextWeek.toISOString(),
      //   maxResults: 50,
      //   singleEvents: true,
      //   orderBy: 'startTime'
      // })

      // const events = response.data.items || []
      // return { events, count: events.length }
    } catch (error) {
      console.error('Error fetching calendar:', error)
      return { events: [], error: error instanceof Error ? error.message : 'Unknown error' }
    }
  }

  private async fetchUserMeetings(userId: string): Promise<any> {
    try {
      // Calendar functionality temporarily disabled
      console.log('Calendar functionality temporarily disabled')
      return { meetings: [], count: 0 }
      
      // Get calendar events with conference data (Meet links)
      // const { getCalendarClient } = await import('./unified-google-client')
      // const { calendar } = await getCalendarClient(userId)

      // const today = new Date()
      // const nextWeek = new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000)

      // const response = await calendar.events.list({
      //   calendarId: 'primary',
      //   timeMin: today.toISOString(),
      //   timeMax: nextWeek.toISOString(),
      //   maxResults: 25,
      //   singleEvents: true,
      //   orderBy: 'startTime'
      // })

      // const events = response.data.items || []
      // // Filter for events with conference data (Meet links) or multiple attendees
      // const meetings = events.filter(event =>
      //   event.conferenceData || (event.attendees && event.attendees.length > 1)
      // )

      // return { meetings, count: meetings.length }
    } catch (error) {
      console.error('Error fetching meetings:', error)
      return { meetings: [], error: error instanceof Error ? error.message : 'Unknown error' }
    }
  }

  private async fetchUserTasks(userId: string): Promise<any> {
    try {
      // Fetch tasks from database directly
      const tasks = await (prisma as any).aITask.findMany({
        where: { userId },
        orderBy: { createdAt: 'desc' },
        take: 20
      })

      // Decrypt task data
      const decryptedTasks = []
      for (const task of tasks) {
        try {
          const decryptedTitle = await this.decryptData(task.title, userId, task.id)
          const decryptedDescription = task.description
            ? await this.decryptData(task.description, userId, task.id)
            : null

          decryptedTasks.push({
            id: task.id,
            title: decryptedTitle,
            description: decryptedDescription,
            status: task.status,
            priority: task.priority,
            type: task.type,
            dueDate: task.dueDate,
            createdAt: task.createdAt
          })
        } catch (decryptError) {
          console.error('Error decrypting task:', decryptError)
          // Skip this task if decryption fails
        }
      }

      return { tasks: decryptedTasks, count: decryptedTasks.length }
    } catch (error) {
      console.error('Error fetching tasks:', error)
      return { tasks: [], error: error instanceof Error ? error.message : 'Unknown error' }
    }
  }

  private async fetchEmailInsights(userId: string): Promise<any> {
    try {
      // Use the AI agent email service to get insights
      const { aiAgentEmailService } = await import('./aiAgentEmail')
      const insights = await aiAgentEmailService.getEmailInsights(userId, 7)

      return { insights }
    } catch (error) {
      console.error('Error fetching email insights:', error)
      return { insights: {}, error: error instanceof Error ? error.message : 'Unknown error' }
    }
  }

  private buildDataContext(comprehensiveData: any): string {
    if (!comprehensiveData) return 'No comprehensive data available.'

    const context = []

    // Email context
    if (comprehensiveData.emails) {
      context.push('EMAIL DATA:')
      if (comprehensiveData.emails.inbox?.emails?.length > 0) {
        context.push(`- Inbox: ${comprehensiveData.emails.inbox.emails.length} emails`)
        const recentEmails = comprehensiveData.emails.inbox.emails.slice(0, 5)
        recentEmails.forEach((email: any, index: number) => {
          context.push(`  ${index + 1}. From: ${email.from}, Subject: "${email.subject}", Date: ${email.date}`)
        })
      }
      if (comprehensiveData.emails.unread?.emails?.length > 0) {
        context.push(`- Unread: ${comprehensiveData.emails.unread.emails.length} emails requiring attention`)
      }
      if (comprehensiveData.emails.sent?.emails?.length > 0) {
        context.push(`- Recent sent: ${comprehensiveData.emails.sent.emails.length} emails`)
      }
    }

    // Calendar context
    if (comprehensiveData.calendar) {
      context.push('\nCALENDAR DATA:')
      if (comprehensiveData.calendar.events?.events?.length > 0) {
        context.push(`- Upcoming events: ${comprehensiveData.calendar.events.events.length}`)
        const upcomingEvents = comprehensiveData.calendar.events.events.slice(0, 5)
        upcomingEvents.forEach((event: any, index: number) => {
          context.push(`  ${index + 1}. "${event.summary}" on ${event.start?.dateTime || event.start?.date}`)
        })
      }
      if (comprehensiveData.calendar.meetings?.meetings?.length > 0) {
        context.push(`- Active meetings: ${comprehensiveData.calendar.meetings.meetings.length}`)
      }
    }

    // Tasks context
    if (comprehensiveData.tasks?.tasks?.length > 0) {
      context.push('\nTASKS DATA:')
      const pendingTasks = comprehensiveData.tasks.tasks.filter((t: any) => t.status !== 'completed')
      context.push(`- Pending tasks: ${pendingTasks.length}`)
      pendingTasks.slice(0, 5).forEach((task: any, index: number) => {
        context.push(`  ${index + 1}. "${task.title}" (${task.priority} priority, due: ${task.dueDate || 'No due date'})`)
      })
    }

    // Insights context
    if (comprehensiveData.insights?.insights) {
      context.push('\nEMAIL INSIGHTS:')
      const insights = comprehensiveData.insights.insights
      if (insights.emailVolume) {
        context.push(`- Email volume: ${insights.emailVolume.total} emails in last 7 days`)
      }
      if (insights.topSenders?.length > 0) {
        context.push(`- Top senders: ${insights.topSenders.slice(0, 3).map((s: any) => s.email).join(', ')}`)
      }
      if (insights.urgentEmails > 0) {
        context.push(`- Urgent emails: ${insights.urgentEmails} requiring immediate attention`)
      }
    }

    // Summary
    if (comprehensiveData.summary) {
      context.push('\nQUICK SUMMARY:')
      context.push(`- Total inbox: ${comprehensiveData.summary.totalInboxEmails}`)
      context.push(`- Unread: ${comprehensiveData.summary.unreadEmailsCount}`)
      context.push(`- Upcoming events: ${comprehensiveData.summary.upcomingEventsCount}`)
      context.push(`- Pending tasks: ${comprehensiveData.summary.pendingTasksCount}`)
    }

    return context.join('\n')
  }

  private buildAgentPrompt(
    userMessage: string,
    conversationHistory: string,
    context: any,
    userBehavior: any,
    knowledgeEntries: any[],
    intent: string,
    comprehensiveData?: any
  ): string {
    // Build personalized context based on user behavior and knowledge
    const personalizedContext = this.buildPersonalizedContext(userBehavior, knowledgeEntries)
    const proactiveInsights = this.generateProactiveInsights(userBehavior, knowledgeEntries, intent)

    // Build comprehensive data context
    const dataContext = this.buildDataContext(comprehensiveData)

    // Get current date/time for context
    const now = new Date()
    const currentDateTime = now.toLocaleDateString() + ' ' + now.toLocaleTimeString()
    const tomorrow = new Date(now.getTime() + 24 * 60 * 60 * 1000).toLocaleDateString()

    return `
🤖 You are an exceptionally intelligent AI assistant with advanced natural language understanding and proactive problem-solving capabilities. You excel at interpreting user intent and taking immediate, helpful action.

🎯 CORE MISSION: Be proactive, intelligent, and solve problems efficiently. Minimize back-and-forth questions by making smart assumptions.

📅 CURRENT CONTEXT:
- Today: ${currentDateTime}
- Tomorrow: ${tomorrow}

🧠 ADVANCED INTELLIGENCE RULES:

1. **IMMEDIATE ACTION PROTOCOL**: When user intent is clear (>75% confidence), take action immediately
2. **SMART INTERPRETATION**:
   - "all day" / "no specific time" / "whole day" → All-day calendar event
   - "tomorrow" → Next calendar day
   - "remind me" → Create task + calendar reminder
   - "schedule meeting" → Create calendar event with Meet link
   - "email about X" → Compose and offer to send email
   - "no time specified" → Default to all-day event

3. **INTELLIGENT DEFAULTS**:
   - Meeting duration: 1 hour unless specified
   - All-day events: When no time specified
   - Task priority: Medium unless urgency indicated
   - Email tone: Professional but friendly
   - Calendar reminders: 15 minutes before

4. **CONTEXT AWARENESS**: Use conversation history and user data to fill information gaps intelligently

USER PROFILE & BEHAVIOR:
${personalizedContext}

PROACTIVE INSIGHTS:
${proactiveInsights}

COMPREHENSIVE USER DATA:
${dataContext}

CONVERSATION HISTORY:
${conversationHistory}

CURRENT REQUEST: "${userMessage}"
DETECTED INTENT: ${intent}

🚀 INTELLIGENT ACTION PROTOCOL:

FOR CALENDAR/REMINDER REQUESTS:
- "Sample Reminder" + "tomorrow" + "all day" → Create all-day event for tomorrow
- "Meeting with X" → Schedule 1-hour meeting with Meet link
- "Remind me to X" → Create task + calendar reminder
- NO TIME SPECIFIED → Default to all-day event

FOR EMAIL REQUESTS:
- Clear recipient + purpose → Compose email immediately
- Professional tone unless specified otherwise
- Include relevant context from user data

FOR TASK REQUESTS:
- Clear description → Create task with smart defaults
- Set due date if mentioned or implied
- Add to calendar if time-sensitive

🎨 RESPONSE STYLE:
- **Conversational & Helpful**: Be natural and friendly
- **Action-Oriented**: "I'll add 'Sample Reminder' to your calendar for tomorrow as an all-day event."
- **Proactive**: Offer related suggestions when appropriate
- **Efficient**: Ask for clarification ONLY when truly necessary (not for obvious defaults)

🔧 SPECIAL HANDLING:
- **"All day" events**: Always create as all-day, never ask for specific times
- **"Tomorrow" requests**: Use next calendar day automatically
- **Vague timing**: Default to all-day events rather than asking
- **Missing details**: Use intelligent defaults based on context

CURRENT TASK: Analyze the user's request and provide an intelligent, action-oriented response that solves their problem efficiently.
- attendees: Array of attendee email addresses
- preferredDate: Preferred date (e.g., "2024-01-15" or "tomorrow")
- preferredTime: Preferred time (e.g., "2:00 PM" or "14:00")
- duration: Meeting duration in minutes (default 60)
- timezone: Timezone (default UTC)
- meetingType: 'virtual', 'in-person', or 'hybrid'
- location: Meeting location (for in-person meetings)
- addMeetLink: Boolean to add Google Meet link (default true for virtual)

TASK ACTIONS:
When user wants to create a task or mentions something they need to do, use the create_task action with these parameters:
- title: Clear, actionable task title
- description: Detailed task description
- type: 'email', 'meeting', 'reminder', 'follow_up', 'bill_payment', 'deadline', 'task'
- priority: 'high', 'medium', 'low'
- dueDate: Due date if mentioned (ISO format)
- reminderDate: Reminder date if needed
- relatedEmails: Array of related email IDs
- relatedContacts: Array of related contact information
- addToCalendar: Boolean to add task as calendar event
- calendarEventTitle: Custom calendar event title
- calendarEventDuration: Duration in minutes for calendar event

Respond naturally and helpfully based on the user's request and intent.
    `
  }

  private async parseActionsFromResponse(response: string, intent: string, userMessage: string): Promise<AIAction[]> {
    const actions: AIAction[] = []

    // Enhanced action detection based on user message intent
    const lowerUserMessage = userMessage.toLowerCase()
    const lowerResponse = response.toLowerCase()

    // Email actions - detect from user message primarily
    if (intent === 'email_compose' ||
        lowerUserMessage.includes('send email') ||
        lowerUserMessage.includes('compose') ||
        lowerUserMessage.includes('mail') ||
        lowerUserMessage.includes('write to') ||
        lowerUserMessage.includes('email to')) {

      const emailAction = await this.extractEmailActionFromUserMessage(userMessage, response)
      if (emailAction) actions.push(emailAction)
    }

    // Calendar/Meeting actions
    if (intent === 'meeting_schedule' ||
        lowerUserMessage.includes('schedule') ||
        lowerUserMessage.includes('meeting') ||
        lowerUserMessage.includes('calendar') ||
        lowerUserMessage.includes('appointment') ||
        lowerUserMessage.includes('book') ||
        lowerUserMessage.includes('add to calendar')) {

      const meetingAction = await this.extractMeetingActionFromUserMessage(userMessage, response)
      if (meetingAction) actions.push(meetingAction)
    }

    // Task actions
    if (intent === 'task_create' ||
        lowerUserMessage.includes('task') ||
        lowerUserMessage.includes('reminder') ||
        lowerUserMessage.includes('todo') ||
        lowerUserMessage.includes('remind me') ||
        lowerUserMessage.includes('create task')) {

      const taskAction = await this.extractTaskActionFromUserMessage(userMessage, response)
      if (taskAction) actions.push(taskAction)
    }

    console.log(`🎯 Detected ${actions.length} actions from user message: "${userMessage}"`)
    return actions
  }

  private async extractEmailActionFromUserMessage(userMessage: string, aiResponse: string): Promise<AIAction | null> {
    try {
      // Extract email details directly from user message
      const emailRegex = /(?:send|email|write|compose).*?(?:to|@)\s*([^\s,]+@[^\s,]+)/gi
      const subjectRegex = /(?:subject|about|regarding)[\s:]*([^.!?]+)/gi

      const emailMatches = [...userMessage.matchAll(emailRegex)]
      const subjectMatches = [...userMessage.matchAll(subjectRegex)]

      if (emailMatches.length > 0) {
        const recipients = emailMatches.map(match => match[1].trim())
        const subject = subjectMatches.length > 0 ? subjectMatches[0][1].trim() : 'Message from AI Assistant'

        return {
          type: 'send_email',
          data: {
            to: recipients,
            subject: subject,
            context: userMessage,
            purpose: 'user_request',
            tone: 'professional'
          },
          status: 'pending'
        }
      }

      // If no direct email found, check if user wants to compose email
      if (userMessage.toLowerCase().includes('send email') ||
          userMessage.toLowerCase().includes('compose') ||
          userMessage.toLowerCase().includes('write email')) {

        return {
          type: 'send_email',
          data: {
            to: [], // Will be filled by AI
            subject: 'Message from AI Assistant',
            context: userMessage,
            purpose: 'user_request',
            tone: 'professional'
          },
          status: 'pending'
        }
      }
    } catch (error) {
      console.error('Error extracting email action:', error)
    }

    return null
  }

  private async extractMeetingActionFromUserMessage(userMessage: string, aiResponse: string): Promise<AIAction | null> {
    try {
      // Extract meeting details from user message
      const titleRegex = /(?:schedule|book|create).*?(?:meeting|appointment|call).*?(?:about|for|regarding)[\s:]*([^.!?]+)/gi
      const dateRegex = /(?:on|for|at)\s*((?:today|tomorrow|monday|tuesday|wednesday|thursday|friday|saturday|sunday|\d{1,2}\/\d{1,2}|\d{1,2}-\d{1,2})[^.!?]*)/gi
      const attendeeRegex = /(?:with|invite)\s*([^.!?]+)/gi

      const titleMatches = [...userMessage.matchAll(titleRegex)]
      const dateMatches = [...userMessage.matchAll(dateRegex)]
      const attendeeMatches = [...userMessage.matchAll(attendeeRegex)]

      if (userMessage.toLowerCase().includes('schedule') ||
          userMessage.toLowerCase().includes('meeting') ||
          userMessage.toLowerCase().includes('calendar')) {

        return {
          type: 'schedule_meeting',
          data: {
            title: titleMatches.length > 0 ? titleMatches[0][1].trim() : 'Meeting',
            description: userMessage,
            dateTime: dateMatches.length > 0 ? dateMatches[0][1].trim() : 'TBD',
            attendees: attendeeMatches.length > 0 ? [attendeeMatches[0][1].trim()] : [],
            duration: 60,
            needsMeetLink: true
          },
          status: 'pending'
        }
      }
    } catch (error) {
      console.error('Error extracting meeting action:', error)
    }

    return null
  }

  private async extractTaskActionFromUserMessage(userMessage: string, aiResponse: string): Promise<AIAction | null> {
    try {
      const lowerMessage = userMessage.toLowerCase()
      
      // Enhanced patterns for task/reminder detection
      const taskPatterns = [
        /(?:remind me|create task|add task|todo)[\s:]*([^.!?]+)/gi,
        /(?:add|create)[\s]*["']([^"']+)["'][\s]*(?:to|for)[\s]*(?:my|the)[\s]*(?:calendar|tasks?|reminders?)/gi,
        /(?:schedule|add)[\s]*["']([^"']+)["'][\s]*(?:for|on)[\s]*(tomorrow|today|monday|tuesday|wednesday|thursday|friday|saturday|sunday)/gi
      ]
      
      // Advanced date patterns
      const dueDatePatterns = [
        /(?:by|due|on|at|for)\s*((?:today|tomorrow|monday|tuesday|wednesday|thursday|friday|saturday|sunday|\d{1,2}\/\d{1,2}|\d{1,2}-\d{1,2})[^.!?]*)/gi,
        /(tomorrow|today|monday|tuesday|wednesday|thursday|friday|saturday|sunday)(?:\s+(?:morning|afternoon|evening|night))?/gi
      ]

      // Check for task-related keywords
      const isTaskRequest = lowerMessage.includes('remind') ||
                           lowerMessage.includes('task') ||
                           lowerMessage.includes('todo') ||
                           lowerMessage.includes('add') && (lowerMessage.includes('calendar') || lowerMessage.includes('reminder'))

      if (isTaskRequest) {
        let taskTitle = 'Task'
        let dueDate = null
        let isAllDay = false

        // Extract task title from various patterns
        for (const pattern of taskPatterns) {
          const matches = [...userMessage.matchAll(pattern)]
          if (matches.length > 0) {
            taskTitle = matches[0][1].trim()
            break
          }
        }

        // If no specific title found, try to extract from quotes
        const quotedText = userMessage.match(/["']([^"']+)["']/g)
        if (quotedText && quotedText.length > 0) {
          taskTitle = quotedText[0].replace(/["']/g, '')
        }

        // Extract due date
        for (const pattern of dueDatePatterns) {
          const matches = [...userMessage.matchAll(pattern)]
          if (matches.length > 0) {
            dueDate = matches[0][1].trim()
            break
          }
        }

                 // Check for all-day indicators
         isAllDay = lowerMessage.includes('all day') || 
                    lowerMessage.includes('no specific time') || 
                    lowerMessage.includes('whole day') ||
                    (dueDate !== null && !lowerMessage.includes('at ') && !lowerMessage.includes(':'))

        // Special handling for "Sample Reminder" example
        if (lowerMessage.includes('sample reminder')) {
          taskTitle = 'Sample Reminder'
          if (lowerMessage.includes('tomorrow')) {
            dueDate = 'tomorrow'
          }
          if (lowerMessage.includes('all day') || lowerMessage.includes('no specific time')) {
            isAllDay = true
          }
        }

        return {
          type: 'create_task',
          data: {
            title: taskTitle,
            description: userMessage,
            dueDate: dueDate,
            priority: 'medium',
            addToCalendar: true,
            isAllDay: isAllDay
          },
          status: 'pending'
        }
      }
    } catch (error) {
      console.error('Error extracting task action:', error)
    }

    return null
  }

  private async extractEmailAction(response: string): Promise<AIAction | null> {
    const emailPrompt = `
    Extract email composition details from this AI response:
    "${response}"

    If the response indicates an email should be sent, extract:
    - recipients (email addresses)
    - subject
    - body content
    - priority level

    Return JSON format or null if no email action is indicated.
    `

    try {
      const result = await model.generateContent(emailPrompt)
      const text = await result.response.text()

      const jsonMatch = text.match(/\{[\s\S]*\}/)
      if (jsonMatch) {
        const emailData = JSON.parse(jsonMatch[0])
        return {
          type: 'send_email',
          data: emailData,
          status: 'pending'
        }
      }
    } catch (error) {
      console.error('Error extracting email action:', error)
    }

    return null
  }

  private async extractMeetingAction(response: string): Promise<AIAction | null> {
    const meetingPrompt = `
    Extract meeting scheduling details from this AI response:
    "${response}"

    If the response indicates a meeting should be scheduled, extract:
    - title
    - description
    - attendees (email addresses)
    - date and time
    - duration
    - location (if mentioned)
    - whether Google Meet link is needed

    Return JSON format or null if no meeting action is indicated.
    `

    try {
      const result = await model.generateContent(meetingPrompt)
      const text = await result.response.text()

      const jsonMatch = text.match(/\{[\s\S]*\}/)
      if (jsonMatch) {
        const meetingData = JSON.parse(jsonMatch[0])
        return {
          type: 'schedule_meeting',
          data: meetingData,
          status: 'pending'
        }
      }
    } catch (error) {
      console.error('Error extracting meeting action:', error)
    }

    return null
  }

  private async extractTaskAction(response: string): Promise<AIAction | null> {
    const taskPrompt = `
    Extract task creation details from this AI response:
    "${response}"

    If the response indicates a task should be created, extract:
    - title
    - description
    - due date
    - priority (high/medium/low)
    - type (email/meeting/reminder/follow_up/bill_payment/deadline)

    Return JSON format or null if no task action is indicated.
    `

    try {
      const result = await model.generateContent(taskPrompt)
      const text = await result.response.text()

      const jsonMatch = text.match(/\{[\s\S]*\}/)
      if (jsonMatch) {
        const taskData = JSON.parse(jsonMatch[0])
        return {
          type: 'create_task',
          data: taskData,
          status: 'pending'
        }
      }
    } catch (error) {
      console.error('Error extracting task action:', error)
    }

    return null
  }

  private formatResponse(text: string): string {
    return text
      // Convert **text** to markdown bold
      .replace(/\*\*(.*?)\*\*/g, '**$1**')
      // Convert *text* to markdown italic
      .replace(/\*(.*?)\*/g, '*$1*')
      // Format email addresses
      .replace(/([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/g, '`$1`')
      // Format dates
      .replace(/(\d{1,2}[\/\-]\d{1,2}[\/\-]\d{2,4})/g, '**$1**')
      // Format times
      .replace(/(\d{1,2}:\d{2}\s*(AM|PM|am|pm))/g, '**$1**')
      // Format currency
      .replace(/(\$\d+(?:\.\d{2})?)/g, '**$1**')
  }

  async getUserBehavior(userId: string): Promise<any> {
    try {
      const behavior = await (prisma as any).userBehavior.findUnique({
        where: { userId }
      })

      if (!behavior) return null

      // Decrypt behavior data
      const decryptedBehavior = {
        emailFrequency: behavior.emailFrequency,
        preferredResponseStyle: behavior.preferredResponseStyle,
        aiInteractionStyle: behavior.aiInteractionStyle
      }

      // Decrypt JSON fields if they exist
      if ((behavior as any).commonEmailTimes) {
        (decryptedBehavior as any).commonEmailTimes = JSON.parse(
          await this.decryptData((behavior as any).commonEmailTimes, userId, behavior.id)
        )
      }

      return decryptedBehavior
    } catch (error) {
      console.error('Error getting user behavior:', error)
      return null
    }
  }

  private async getRelevantKnowledge(userId: string, message: string): Promise<any[]> {
    try {
      // Get knowledge entries that might be relevant to the message
      const entries = await (prisma as any).knowledgeEntry.findMany({
        where: {
          userId,
          confidence: { gte: 0.3 } // Only get entries with reasonable confidence
        },
        orderBy: { confidence: 'desc' },
        take: 10
      })

      const relevantEntries = []
      for (const entry of entries) {
        const decryptedKey = await this.decryptData(entry.key, userId, entry.id)
        const decryptedValue = await this.decryptData(entry.value, userId, entry.id)

        // Simple relevance check - could be improved with embeddings
        if (message.toLowerCase().includes(decryptedKey.toLowerCase()) ||
            decryptedKey.toLowerCase().includes(message.toLowerCase())) {
          relevantEntries.push({
            type: entry.type,
            category: entry.category,
            key: decryptedKey,
            value: decryptedValue,
            confidence: entry.confidence
          })
        }
      }

      return relevantEntries
    } catch (error) {
      console.error('Error getting relevant knowledge:', error)
      return []
    }
  }

  private async executeActions(actions: AIAction[], userId: string, conversationId: string): Promise<void> {
    for (const action of actions) {
      try {
        switch (action.type) {
          case 'send_email':
            await this.executeSendEmail(action, userId)
            break
          case 'schedule_meeting':
            await this.executeScheduleMeeting(action, userId)
            break
          case 'create_task':
            await this.executeCreateTask(action, userId, conversationId)
            break
          case 'analyze_emails':
            await this.executeAnalyzeEmails(action, userId)
            break
        }
        action.status = 'completed'
      } catch (error) {
        console.error(`Error executing action ${action.type}:`, error)
        action.status = 'failed'
        action.result = { error: error instanceof Error ? error.message : 'Unknown error' }
      }
    }
  }

  private async executeSendEmail(action: AIAction, userId: string): Promise<void> {
    try {
      const { aiAgentEmailService } = await import('./aiAgentEmail')

      // First compose the email using AI
      const composeRequest = {
        to: action.data.to,
        cc: action.data.cc,
        bcc: action.data.bcc,
        subject: action.data.subject,
        context: action.data.context,
        tone: action.data.tone || 'professional',
        purpose: action.data.purpose || 'general',
        keyPoints: action.data.keyPoints,
        attachments: action.data.attachments
      }

      const emailContent = await aiAgentEmailService.composeEmail(userId, composeRequest)

      // Then send the composed email
      const sendResult = await aiAgentEmailService.sendComposedEmail(userId, {
        to: Array.isArray(action.data.to) ? action.data.to.join(', ') : action.data.to,
        subject: emailContent.subject,
        htmlBody: emailContent.htmlBody,
        textBody: emailContent.textBody,
        attachments: action.data.attachments
      })

      // Track email behavior if successful
      if (sendResult.success) {
        await this.trackUserBehavior(userId, 'email_sent', {
          to: action.data.to,
          subject: emailContent.subject,
          tone: action.data.tone,
          purpose: action.data.purpose,
          timestamp: new Date()
        })
      }

      action.result = {
        emailContent,
        sendResult,
        message: sendResult.success
          ? `Email sent successfully to ${action.data.to}`
          : `Failed to send email: ${sendResult.error}`
      }
    } catch (error) {
      console.error('Error sending email:', error)
      action.result = { error: error instanceof Error ? error.message : 'Unknown error' }
    }
  }

  private async executeScheduleMeeting(action: AIAction, userId: string): Promise<void> {
    try {
      const { aiAgentCalendarService } = await import('./aiAgentCalendar')

      const scheduleRequest = {
        context: action.data.context || action.data.description || 'Meeting scheduled via AI agent',
        attendees: action.data.attendees || [],
        preferredDate: action.data.preferredDate,
        preferredTime: action.data.preferredTime,
        duration: action.data.duration || 60,
        timezone: action.data.timezone || 'UTC',
        meetingType: action.data.meetingType || 'virtual',
        location: action.data.location,
        addMeetLink: action.data.addMeetLink !== false // Default to true
      }

      const result = await aiAgentCalendarService.parseAndScheduleMeeting(userId, scheduleRequest)

      if (result.success) {
        // Track meeting behavior
        await this.trackUserBehavior(userId, 'meeting_scheduled', {
          title: result.meetingDetails.title,
          attendees: action.data.attendees,
          duration: action.data.duration,
          meetingType: action.data.meetingType,
          timestamp: new Date()
        })

        action.result = {
          meeting: result.meetingDetails,
          event: result.event,
          meetSpace: result.meetSpace,
          message: `Meeting "${result.meetingDetails.title}" scheduled successfully for ${result.meetingDetails.startTime.toLocaleString()}`
        }
      } else {
        action.result = { error: result.error || 'Failed to schedule meeting' }
      }
    } catch (error) {
      console.error('Error scheduling meeting:', error)
      action.result = { error: error instanceof Error ? error.message : 'Unknown error' }
    }
  }

  private async executeCreateTask(action: AIAction, userId: string, conversationId: string): Promise<void> {
    try {
      const { aiAgentTaskService } = await import('./aiAgentTasks')

      const taskRequest = {
        title: action.data.title,
        description: action.data.description,
        type: action.data.type || 'task',
        priority: action.data.priority || 'medium',
        dueDate: action.data.dueDate ? new Date(action.data.dueDate) : undefined,
        reminderDate: action.data.reminderDate ? new Date(action.data.reminderDate) : undefined,
        relatedEmails: action.data.relatedEmails,
        relatedContacts: action.data.relatedContacts,
        relatedMeetings: action.data.relatedMeetings,
        addToCalendar: action.data.addToCalendar || false,
        calendarEventTitle: action.data.calendarEventTitle,
        calendarEventDuration: action.data.calendarEventDuration,
        isAllDay: action.data.isAllDay || false
      }

      const result = await aiAgentTaskService.createTask(userId, taskRequest, conversationId)

      if (result.success) {
        // Track task creation behavior
        await this.trackUserBehavior(userId, 'task_created', {
          title: result.task.title,
          type: result.task.type,
          priority: result.task.priority,
          addedToCalendar: !!result.calendarEvent,
          timestamp: new Date()
        })

        action.result = {
          task: result.task,
          calendarEvent: result.calendarEvent,
          message: `Task "${result.task.title}" created successfully${result.calendarEvent ? ' and added to calendar' : ''}`
        }
      } else {
        action.result = { error: result.error || 'Failed to create task' }
      }
    } catch (error) {
      console.error('Error creating task:', error)
      action.result = { error: error instanceof Error ? error.message : 'Unknown error' }
    }
  }

  private async executeAnalyzeEmails(action: AIAction, userId: string): Promise<void> {
    try {
      const { aiAgentEmailService } = await import('./aiAgentEmail')

      const analysisRequest = {
        query: action.data.query,
        dateFrom: action.data.dateFrom ? new Date(action.data.dateFrom) : undefined,
        dateTo: action.data.dateTo ? new Date(action.data.dateTo) : undefined,
        category: action.data.category || 'all',
        limit: action.data.limit || 20
      }

      const analysis = await aiAgentEmailService.analyzeEmails(userId, analysisRequest)

      action.result = {
        analysis,
        message: `Analyzed ${analysis.totalEmails} emails. Found ${analysis.unreadCount} unread emails and ${analysis.importantEmails.length} important emails.`
      }
    } catch (error) {
      console.error('Error analyzing emails:', error)
      action.result = { error: error instanceof Error ? error.message : 'Unknown error' }
    }
  }

  // Email analysis methods
  async analyzeEmails(emails: any[], userId: string, query?: string): Promise<EmailSummary[]> {
    const analysisPrompt = `
    Analyze the following emails and provide structured summaries for each email.

    ${query ? `Specific user query: ${query}` : 'Provide general analysis focusing on importance and actionable items.'}

    For each email, provide:
    1. Importance level (high/medium/low)
    2. Brief summary (2-3 sentences)
    3. Category (work, personal, promotional, bills, social, etc.)
    4. Action items (list of specific actions needed)
    5. Sentiment (positive/neutral/negative)

    Emails to analyze:
    ${emails.map((email, index) => `
    Email ${index + 1}:
    From: ${email.from}
    Subject: ${email.subject}
    Date: ${email.date}
    Content: ${email.body?.substring(0, 1000) || email.snippet}
    `).join('\n')}

    Return as JSON array of EmailSummary objects.
    `

    try {
      const result = await model.generateContent(analysisPrompt)
      const response = await result.response
      const text = response.text()

      const jsonMatch = text.match(/\[[\s\S]*\]/)
      if (jsonMatch) {
        const summaries = JSON.parse(jsonMatch[0])
        return summaries.map((summary: any, index: number) => ({
          id: emails[index]?.id || `email-${index}`,
          subject: emails[index]?.subject || 'No subject',
          from: emails[index]?.from || 'Unknown sender',
          date: new Date(emails[index]?.date || Date.now()),
          importance: summary.importance || 'medium',
          summary: summary.summary || 'No summary available',
          category: summary.category || 'general',
          actionItems: summary.actionItems || [],
          sentiment: summary.sentiment || 'neutral'
        }))
      }

      throw new Error('Invalid response format')
    } catch (error) {
      console.error('Error analyzing emails:', error)
      throw error
    }
  }

  // Conversation management methods
  async getConversations(userId: string, limit: number = 20): Promise<any[]> {
    const conversations = await (prisma as any).conversation.findMany({
      where: { userId, status: 'active' },
      orderBy: { updatedAt: 'desc' },
      take: limit,
      include: {
        messages: {
          orderBy: { createdAt: 'desc' },
          take: 1 // Get last message for preview
        }
      }
    })

    const decryptedConversations = []
    for (const conv of conversations) {
      const decryptedTitle = await this.decryptData(conv.title, userId, conv.id)
      let lastMessage = null

      if (conv.messages.length > 0) {
        const msg = conv.messages[0]
        const decryptedContent = await this.decryptData(msg.content, userId, msg.id)
        lastMessage = {
          role: msg.role,
          content: decryptedContent.substring(0, 100) + (decryptedContent.length > 100 ? '...' : ''),
          timestamp: msg.createdAt
        }
      }

      decryptedConversations.push({
        id: conv.id,
        title: decryptedTitle,
        lastMessage,
        updatedAt: conv.updatedAt,
        createdAt: conv.createdAt
      })
    }

    return decryptedConversations
  }

  async getConversationMessages(conversationId: string, userId: string): Promise<AIMessage[]> {
    const conversation = await (prisma as any).conversation.findFirst({
      where: { id: conversationId, userId },
      include: {
        messages: {
          orderBy: { createdAt: 'asc' }
        }
      }
    })

    if (!conversation) {
      throw new Error('Conversation not found')
    }

    const messages: AIMessage[] = []
    for (const msg of conversation.messages) {
      const decryptedContent = await this.decryptData(msg.content, userId, msg.id)
      const decryptedMetadata = msg.metadata
        ? JSON.parse(await this.decryptData(msg.metadata, userId, msg.id))
        : undefined

      messages.push({
        id: msg.id,
        role: msg.role as 'user' | 'assistant' | 'system',
        content: decryptedContent,
        timestamp: msg.createdAt,
        metadata: decryptedMetadata
      })
    }

    return messages
  }

  // Knowledge management methods
  async updateUserKnowledge(
    userId: string,
    type: string,
    category: string,
    key: string,
    value: string,
    confidence: number = 0.7,
    source: string = 'conversation'
  ): Promise<void> {
    const salt = modernEncryption.generateEncryptionSalt(userId, `knowledge-${type}-${Date.now()}`, 'chat')
    const encryptedKey = await modernEncryption.encryptData(key, userId, 'chat', salt)
    const encryptedValue = await modernEncryption.encryptData(value, userId, 'chat', salt)

    await (prisma as any).knowledgeEntry.upsert({
      where: {
        userId_type_key: {
          userId,
          type,
          key: encryptedKey
        }
      },
      update: {
        value: encryptedValue,
        confidence,
        source,
        updatedAt: new Date()
      },
      create: {
        userId,
        type,
        category,
        key: encryptedKey,
        value: encryptedValue,
        confidence,
        source,
        encryptionSalt: salt
      }
    })
  }

  async updateUserBehavior(userId: string, behaviorData: any): Promise<void> {
    const salt = modernEncryption.generateEncryptionSalt(userId, `behavior-${Date.now()}`, 'chat')

    const encryptedData: any = {}

    // Encrypt JSON fields
    if (behaviorData.commonEmailTimes) {
      encryptedData.commonEmailTimes = await modernEncryption.encryptData(
        JSON.stringify(behaviorData.commonEmailTimes),
        userId,
        'chat',
        salt
      )
    }

    if (behaviorData.frequentContacts) {
      encryptedData.frequentContacts = await modernEncryption.encryptData(
        JSON.stringify(behaviorData.frequentContacts),
        userId,
        'chat',
        salt
      )
    }

    await (prisma as any).userBehavior.upsert({
      where: { userId },
      update: {
        ...behaviorData,
        ...encryptedData,
        updatedAt: new Date()
      },
      create: {
        userId,
        ...behaviorData,
        ...encryptedData,
        encryptionSalt: salt
      }
    })
  }

  async trackUserBehavior(userId: string, actionType: string, actionData: any): Promise<void> {
    try {
      const salt = modernEncryption.generateEncryptionSalt(userId, `behavior-${actionType}-${Date.now()}`, 'chat')
      const encryptedActionData = await modernEncryption.encryptData(JSON.stringify(actionData), userId, 'chat', salt)

      // Get or create user behavior record
      let userBehavior = await (prisma as any).userBehavior.findUnique({
        where: { userId }
      })

      if (!userBehavior) {
        userBehavior = await (prisma as any).userBehavior.create({
          data: {
            userId,
            encryptionSalt: salt
          }
        })
      }

      // Update behavior counters based on action type
      const updateData: any = { updatedAt: new Date() }

      switch (actionType) {
        case 'email_sent':
          updateData.totalEmails = { increment: 1 }
          break
        case 'meeting_scheduled':
          updateData.totalMeetings = { increment: 1 }
          break
        case 'task_created':
          updateData.totalTasks = { increment: 1 }
          break
        case 'chat_message':
          updateData.totalChatMessages = { increment: 1 }
          break
      }

      await (prisma as any).userBehavior.update({
        where: { userId },
        data: updateData
      })

      // Store detailed behavior data in knowledge entries
      await (prisma as any).knowledgeEntry.create({
        data: {
          userId,
          type: 'behavior',
          category: actionType,
          key: `behavior_${actionType}_${Date.now()}`,
          value: encryptedActionData,
          confidence: 1.0,
          source: 'user_interaction',
          encryptionSalt: salt
        }
      })

    } catch (error) {
      console.error('Error tracking user behavior:', error)
    }
  }

  async updateKnowledgeGraph(userId: string, userMessage: string, aiResponse: string): Promise<void> {
    try {
      const salt = modernEncryption.generateEncryptionSalt(userId, 'knowledge-' + Date.now(), 'chat')

      // Extract key concepts and preferences from the conversation
      const knowledgePrompt = `
      Analyze this conversation and extract key insights about the user's preferences, patterns, and needs:

      User: "${userMessage}"
      Assistant: "${aiResponse}"

      Extract:
      1. User preferences (communication style, timing, priorities)
      2. Work patterns (meeting preferences, task management style)
      3. Contact relationships (frequent collaborators, important contacts)
      4. Topics of interest
      5. Pain points or challenges mentioned

      Return JSON with extracted insights:
      {
        "preferences": ["preference1", "preference2"],
        "patterns": ["pattern1", "pattern2"],
        "contacts": ["contact1", "contact2"],
        "topics": ["topic1", "topic2"],
        "challenges": ["challenge1", "challenge2"],
        "confidence": 0.8
      }
      `

      const result = await model.generateContent(knowledgePrompt)
      const response = await result.response
      const text = response.text()

      const jsonMatch = text.match(/\{[\s\S]*\}/)
      if (jsonMatch) {
        const insights = JSON.parse(jsonMatch[0])
        const resourceId = `insight_${Date.now()}`
        const encryptedInsights = await this.encryptData(JSON.stringify(insights), userId, resourceId)

        // Store knowledge entry
        await (prisma as any).knowledgeEntry.create({
          data: {
            userId,
            type: 'conversation_insight',
            category: 'user_preferences',
            key: resourceId,
            value: encryptedInsights.encryptedData,
            confidence: insights.confidence || 0.5,
            source: 'conversation_analysis',
            encryptionSalt: salt
          }
        })
      }

    } catch (error) {
      console.error('Error updating knowledge graph:', error)
    }
  }

  async getPersonalizedSuggestions(userId: string): Promise<{
    emailSuggestions: string[]
    taskSuggestions: string[]
    meetingSuggestions: string[]
    success: boolean
  }> {
    try {
      // Get user behavior and knowledge
      const userBehavior = await this.getUserBehavior(userId)
      const knowledgeEntries = await (prisma as any).knowledgeEntry.findMany({
        where: { userId },
        orderBy: { createdAt: 'desc' },
        take: 20
      })

      // Decrypt and analyze knowledge
      const insights = []
      for (const entry of knowledgeEntries) {
        try {
          const decryptedContent = await this.decryptData(entry.value, userId, entry.key || entry.id)
          insights.push({
            type: entry.type,
            category: entry.category,
            content: JSON.parse(decryptedContent),
            confidence: entry.confidence
          })
        } catch (error) {
          console.error('Error decrypting knowledge entry:', error)
        }
      }

      // Generate personalized suggestions
      const suggestionPrompt = `
      Based on this user's behavior and preferences, suggest personalized actions:

      User Behavior: ${JSON.stringify(userBehavior)}
      Recent Insights: ${JSON.stringify(insights.slice(0, 10))}

      Generate suggestions for:
      1. Email actions (compose, follow-up, organize)
      2. Task creation (based on patterns and deadlines)
      3. Meeting scheduling (based on preferences and contacts)

      Return JSON:
      {
        "emailSuggestions": ["suggestion1", "suggestion2"],
        "taskSuggestions": ["suggestion1", "suggestion2"],
        "meetingSuggestions": ["suggestion1", "suggestion2"]
      }
      `

      const result = await model.generateContent(suggestionPrompt)
      const response = await result.response
      const text = response.text()

      const jsonMatch = text.match(/\{[\s\S]*\}/)
      if (jsonMatch) {
        const suggestions = JSON.parse(jsonMatch[0])
        return {
          ...suggestions,
          success: true
        }
      }

      return {
        emailSuggestions: [],
        taskSuggestions: [],
        meetingSuggestions: [],
        success: true
      }

    } catch (error) {
      console.error('Error getting personalized suggestions:', error)
      return {
        emailSuggestions: [],
        taskSuggestions: [],
        meetingSuggestions: [],
        success: false
      }
    }
  }

  private buildPersonalizedContext(userBehavior: any, knowledgeEntries: any[]): string {
    if (!userBehavior && knowledgeEntries.length === 0) {
      return 'No personalization data available yet. Focus on learning user preferences.'
    }

    let context = 'PERSONALIZED USER PROFILE:\n'

    if (userBehavior) {
      context += `Communication Patterns:
- Emails sent: ${userBehavior.totalEmails || 0}
- Meetings scheduled: ${userBehavior.totalMeetings || 0}
- Tasks created: ${userBehavior.totalTasks || 0}
- Chat messages: ${userBehavior.totalChatMessages || 0}
- Most active time: ${this.getMostActiveTime(userBehavior)}
- Preferred communication style: ${this.inferCommunicationStyle(userBehavior)}
`
    }

    if (knowledgeEntries.length > 0) {
      context += `\nLearned Preferences:
${knowledgeEntries.map(entry => `- ${entry.key}: ${entry.value}`).join('\n')}
`
    }

    return context
  }

  private generateProactiveInsights(userBehavior: any, knowledgeEntries: any[], intent: string): string {
    const insights = []

    // Time-based insights
    const currentHour = new Date().getHours()
    if (currentHour >= 9 && currentHour <= 17) {
      insights.push('User is likely in work hours - prioritize professional tasks and communications')
    }

    // Behavior-based insights
    if (userBehavior?.totalEmails > 10) {
      insights.push('User is an active email communicator - offer email templates and automation suggestions')
    }

    if (userBehavior?.totalMeetings > 5) {
      insights.push('User frequently schedules meetings - suggest calendar optimization and meeting best practices')
    }

    if (userBehavior?.totalTasks > 15) {
      insights.push('User is task-oriented - offer advanced task management features and productivity tips')
    }

    // Intent-based proactive suggestions
    switch (intent) {
      case 'email_compose':
        insights.push('Consider suggesting email templates, tone adjustments, or recipient insights')
        break
      case 'meeting_schedule':
        insights.push('Offer optimal meeting times, agenda templates, or conflict resolution')
        break
      case 'task_create':
        insights.push('Suggest task prioritization, deadline optimization, or related task grouping')
        break
    }

    return insights.length > 0
      ? `PROACTIVE SUGGESTIONS:\n${insights.map(insight => `- ${insight}`).join('\n')}`
      : 'No specific proactive insights available'
  }

  private getMostActiveTime(userBehavior: any): string {
    // Simple heuristic - in a real implementation, you'd track timestamps
    const totalActions = (userBehavior.totalEmails || 0) + (userBehavior.totalMeetings || 0) + (userBehavior.totalTasks || 0)

    if (totalActions > 20) return 'High activity user - likely active during business hours'
    if (totalActions > 10) return 'Moderate activity user - regular business hours'
    return 'New user - still learning patterns'
  }

  private inferCommunicationStyle(userBehavior: any): string {
    const emailRatio = (userBehavior.totalEmails || 0) / Math.max((userBehavior.totalChatMessages || 0), 1)

    if (emailRatio > 2) return 'Formal - prefers email communication'
    if (emailRatio < 0.5) return 'Casual - prefers chat and quick interactions'
    return 'Balanced - uses both formal and informal communication'
  }

  // Enhanced conversation memory methods
  async getConversationContext(conversationId: string, limit: number = 10): Promise<string> {
    try {
      const messages = await (prisma as any).message.findMany({
        where: { conversationId },
        orderBy: { createdAt: 'desc' },
        take: limit,
        select: {
          role: true,
          content: true,
          createdAt: true,
          metadata: true
        }
      })

      if (messages.length === 0) return 'No previous conversation context'

      // Decrypt and format messages
      const contextMessages = await Promise.all(
        messages.reverse().map(async (msg: any) => {
          const decryptedContent = await this.decryptData(msg.content, msg.userId || 'unknown', msg.id)
          const timestamp = format(msg.createdAt, 'MMM dd, HH:mm')

          // Include action context if available
          let actionContext = ''
          if (msg.metadata && typeof msg.metadata === 'object' && 'actions' in msg.metadata) {
            const actions = (msg.metadata as any).actions || []
            if (actions.length > 0) {
              actionContext = ` [Actions: ${actions.map((a: any) => a.type).join(', ')}]`
            }
          }

          return `[${timestamp}] ${msg.role}: ${decryptedContent}${actionContext}`
        })
      )

      return contextMessages.join('\n')
    } catch (error) {
      console.error('Error getting conversation context:', error)
      return 'Error retrieving conversation context'
    }
  }

  // Intelligent suggestion system
  async getContextualSuggestions(userId: string, currentMessage: string, intent: string): Promise<string[]> {
    try {
      const userBehavior = await this.getUserBehavior(userId)
      const knowledgeEntries = await (prisma as any).knowledgeEntry.findMany({
        where: { userId },
        orderBy: { createdAt: 'desc' },
        take: 10
      })

      const suggestionPrompt = `
      Based on the user's current message and behavior patterns, suggest 3-5 contextual quick actions or responses:

      Current message: "${currentMessage}"
      Intent: ${intent}
      User behavior: ${JSON.stringify(userBehavior, null, 2)}
      Knowledge: ${knowledgeEntries.map((k: any) => `${k.key}: ${k.category}`).join(', ')}

      Provide suggestions as a JSON array of strings, each being a actionable suggestion the user might want to take.
      Focus on practical, immediate actions based on the context.

      Example format: ["Schedule a follow-up meeting", "Create a task reminder", "Draft a response email"]
      `

      const result = await model.generateContent(suggestionPrompt)
      const response = await result.response
      const text = response.text()

      const jsonMatch = text.match(/\[[\s\S]*?\]/)
      if (jsonMatch) {
        const suggestions = JSON.parse(jsonMatch[0])
        return Array.isArray(suggestions) ? suggestions : []
      }

      return []
    } catch (error) {
      console.error('Error getting contextual suggestions:', error)
      return []
    }
  }
}

export const aiAgentService = new AIAgentService()
