import { prisma } from '../prisma'
import { modernEncryption } from '../encryption'
// import { getCalendarClient } from './unified-google-client' // TODO: Enable when calendar is available
const getCalendarClient = async (userId: string) => {
  throw new Error('Calendar client not available yet - Prisma client needs regeneration')
}
import { GoogleGenerativeAI } from '@google/generative-ai'

const genAI = new GoogleGenerativeAI(process.env.GEMINI_API || "AIzaSyAwHr4qa8SLtb7RMqaSRFzgAo6YwpP2Ga0")
const model = genAI.getGenerativeModel({ model: process.env.GEMINI_MODEL || "gemini-2.5-flash-lite-preview-06-17" })

export interface TaskRequest {
  title: string
  description?: string
  type: 'email' | 'meeting' | 'reminder' | 'follow_up' | 'bill_payment' | 'deadline' | 'task'
  priority: 'high' | 'medium' | 'low'
  dueDate?: Date
  reminderDate?: Date
  relatedEmails?: string[]
  relatedContacts?: string[]
  relatedMeetings?: string[]
  addToCalendar?: boolean
  calendarEventTitle?: string
  calendarEventDuration?: number // in minutes
}

export interface TaskUpdate {
  title?: string
  description?: string
  status?: 'pending' | 'in_progress' | 'completed' | 'cancelled'
  priority?: 'high' | 'medium' | 'low'
  dueDate?: Date
  reminderDate?: Date
  userFeedback?: string
}

export interface TaskFilter {
  status?: string[]
  type?: string[]
  priority?: string[]
  dueDateFrom?: Date
  dueDateTo?: Date
  search?: string
  limit?: number
  offset?: number
}

export interface TaskSuggestion {
  title: string
  description: string
  type: string
  priority: string
  dueDate?: Date
  confidence: number
  reason: string
  relatedContext: string
}

class AIAgentTaskService {

  private async encryptData(data: string, userId: string, resourceId: string): Promise<string> {
    return await modernEncryption.encryptData(data, userId, 'calendar', resourceId)
  }

  private async decryptData(encryptedData: string, userId: string, resourceId: string): Promise<string> {
    return await modernEncryption.decryptData(encryptedData, userId, 'calendar', resourceId)
  }

  async createTask(userId: string, taskRequest: TaskRequest, conversationId?: string): Promise<{
    task: any
    calendarEvent?: any
    success: boolean
    error?: string
  }> {
    try {
      const taskId = 'task-' + Date.now()
      const salt = modernEncryption.generateEncryptionSalt(userId, taskId, 'calendar')

      // Create the task in database
      const task = await (prisma as any).aITask.create({
        data: {
          userId,
          conversationId,
          title: await this.encryptData(taskRequest.title, userId, taskId),
          description: taskRequest.description ? await this.encryptData(taskRequest.description, userId, taskId) : null,
          type: taskRequest.type,
          priority: taskRequest.priority,
          dueDate: taskRequest.dueDate,
          reminderDate: taskRequest.reminderDate,
          relatedEmails: taskRequest.relatedEmails ? await this.encryptData(JSON.stringify(taskRequest.relatedEmails), userId, taskId) : null,
          relatedContacts: taskRequest.relatedContacts ? await this.encryptData(JSON.stringify(taskRequest.relatedContacts), userId, taskId) : null,
          relatedMeetings: taskRequest.relatedMeetings ? await this.encryptData(JSON.stringify(taskRequest.relatedMeetings), userId, taskId) : null,
          encryptionSalt: salt
        }
      })

      let calendarEvent = null

      // Add to calendar if requested and has due date
      if (taskRequest.addToCalendar && taskRequest.dueDate) {
        try {
          const { calendar } = await getCalendarClient(userId)
          
          const eventTitle = taskRequest.calendarEventTitle || `Task: ${taskRequest.title}`
          const duration = taskRequest.calendarEventDuration || 30 // Default 30 minutes
          const startTime = new Date(taskRequest.dueDate)
          const endTime = new Date(startTime.getTime() + duration * 60000)

          const googleEvent = {
            summary: eventTitle,
            description: `Task: ${taskRequest.title}\n\n${taskRequest.description || ''}\n\nPriority: ${taskRequest.priority}\nType: ${taskRequest.type}`,
            start: {
              dateTime: startTime.toISOString(),
              timeZone: 'UTC'
            },
            end: {
              dateTime: endTime.toISOString(),
              timeZone: 'UTC'
            },
            reminders: {
              useDefault: false,
              overrides: [
                { method: 'popup', minutes: 15 },
                { method: 'email', minutes: 60 }
              ]
            }
          }

          const response = await calendar.events.insert({
            calendarId: 'primary',
            requestBody: googleEvent,
            sendUpdates: 'none'
          })

          calendarEvent = response.data

          // Update task with calendar event ID
          await (prisma as any).aITask.update({
            where: { id: task.id },
            data: {
              relatedMeetings: await this.encryptData(JSON.stringify([response.data.id]), userId, task.id)
            }
          })

        } catch (calendarError) {
          console.error('Error adding task to calendar:', calendarError)
          // Don't fail the task creation if calendar fails
        }
      }

      return {
        task: {
          id: task.id,
          title: taskRequest.title,
          description: taskRequest.description,
          type: task.type,
          priority: task.priority,
          status: task.status,
          dueDate: task.dueDate,
          createdAt: task.createdAt
        },
        calendarEvent,
        success: true
      }

    } catch (error) {
      console.error('Error creating task:', error)
      return {
        task: null,
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create task'
      }
    }
  }

  async getTasks(userId: string, filter: TaskFilter = {}): Promise<{
    tasks: any[]
    total: number
    success: boolean
    error?: string
  }> {
    try {
      const where: any = { userId }

      if (filter.status && filter.status.length > 0) {
        where.status = { in: filter.status }
      }

      if (filter.type && filter.type.length > 0) {
        where.type = { in: filter.type }
      }

      if (filter.priority && filter.priority.length > 0) {
        where.priority = { in: filter.priority }
      }

      if (filter.dueDateFrom || filter.dueDateTo) {
        where.dueDate = {}
        if (filter.dueDateFrom) where.dueDate.gte = filter.dueDateFrom
        if (filter.dueDateTo) where.dueDate.lte = filter.dueDateTo
      }

      const [tasks, total] = await Promise.all([
        (prisma as any).aITask.findMany({
          where,
          orderBy: [
            { priority: 'desc' },
            { dueDate: 'asc' },
            { createdAt: 'desc' }
          ],
          take: filter.limit || 50,
          skip: filter.offset || 0
        }),
        (prisma as any).aITask.count({ where })
      ])

      // Decrypt and format tasks
      const decryptedTasks = await Promise.all(
        tasks.map(async (task) => {
          const decryptedTitle = await this.decryptData(task.title, task.userId, task.id)
          const decryptedDescription = task.description
            ? await this.decryptData(task.description, task.userId, task.id)
            : null

          let relatedEmails = []
          let relatedContacts = []
          let relatedMeetings = []

          try {
            if (task.relatedEmails) {
              relatedEmails = JSON.parse(await this.decryptData(task.relatedEmails, task.userId, task.id))
            }
            if (task.relatedContacts) {
              relatedContacts = JSON.parse(await this.decryptData(task.relatedContacts, task.userId, task.id))
            }
            if (task.relatedMeetings) {
              relatedMeetings = JSON.parse(await this.decryptData(task.relatedMeetings, task.userId, task.id))
            }
          } catch (error) {
            console.error('Error decrypting task relations:', error)
          }

          return {
            id: task.id,
            title: decryptedTitle,
            description: decryptedDescription,
            type: task.type,
            status: task.status,
            priority: task.priority,
            dueDate: task.dueDate,
            reminderDate: task.reminderDate,
            completedAt: task.completedAt,
            relatedEmails,
            relatedContacts,
            relatedMeetings,
            createdAt: task.createdAt,
            updatedAt: task.updatedAt
          }
        })
      )

      // Apply search filter if provided
      let filteredTasks = decryptedTasks
      if (filter.search) {
        const searchTerm = filter.search.toLowerCase()
        filteredTasks = decryptedTasks.filter(task => 
          task.title.toLowerCase().includes(searchTerm) ||
          (task.description && task.description.toLowerCase().includes(searchTerm))
        )
      }

      return {
        tasks: filteredTasks,
        total: filter.search ? filteredTasks.length : total,
        success: true
      }

    } catch (error) {
      console.error('Error getting tasks:', error)
      return {
        tasks: [],
        total: 0,
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get tasks'
      }
    }
  }

  async updateTask(userId: string, taskId: string, update: TaskUpdate): Promise<{
    task?: any
    success: boolean
    error?: string
  }> {
    try {
      // Get existing task to verify ownership and get salt
      const existingTask = await (prisma as any).aITask.findFirst({
        where: { id: taskId, userId }
      })

      if (!existingTask) {
        return {
          success: false,
          error: 'Task not found or access denied'
        }
      }

      const updateData: any = {}

      if (update.title) {
        updateData.title = await this.encryptData(update.title, userId, taskId)
      }

      if (update.description !== undefined) {
        updateData.description = update.description
          ? await this.encryptData(update.description, userId, taskId)
          : null
      }

      if (update.status) {
        updateData.status = update.status
        if (update.status === 'completed') {
          updateData.completedAt = new Date()
        }
      }

      if (update.priority) updateData.priority = update.priority
      if (update.dueDate !== undefined) updateData.dueDate = update.dueDate
      if (update.reminderDate !== undefined) updateData.reminderDate = update.reminderDate

      if (update.userFeedback) {
        updateData.userFeedback = await this.encryptData(update.userFeedback, userId, taskId)
      }

      const updatedTask = await (prisma as any).aITask.update({
        where: { id: taskId },
        data: updateData
      })

      return {
        task: {
          id: updatedTask.id,
          status: updatedTask.status,
          priority: updatedTask.priority,
          dueDate: updatedTask.dueDate,
          completedAt: updatedTask.completedAt,
          updatedAt: updatedTask.updatedAt
        },
        success: true
      }

    } catch (error) {
      console.error('Error updating task:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to update task'
      }
    }
  }

  async deleteTask(userId: string, taskId: string): Promise<{
    success: boolean
    error?: string
  }> {
    try {
      const deletedTask = await (prisma as any).aITask.deleteMany({
        where: { id: taskId, userId }
      })

      if (deletedTask.count === 0) {
        return {
          success: false,
          error: 'Task not found or access denied'
        }
      }

      return { success: true }

    } catch (error) {
      console.error('Error deleting task:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to delete task'
      }
    }
  }

  async suggestTasks(userId: string, context: string, emailContext?: any[]): Promise<{
    suggestions: TaskSuggestion[]
    success: boolean
    error?: string
  }> {
    try {
      const suggestionPrompt = `
      Based on the following context, suggest intelligent tasks that would be helpful:

      User Context: "${context}"
      
      ${emailContext && emailContext.length > 0 ? `
      Recent Email Context:
      ${emailContext.map((email, index) => `
      Email ${index + 1}:
      From: ${email.from}
      Subject: ${email.subject}
      Snippet: ${email.snippet}
      Date: ${email.date}
      `).join('\n')}
      ` : ''}

      Suggest up to 5 relevant tasks with:
      - title: Clear, actionable task title
      - description: Detailed description
      - type: email, meeting, reminder, follow_up, bill_payment, deadline, task
      - priority: high, medium, low
      - dueDate: Suggested due date (ISO format) if applicable
      - confidence: 0.0-1.0 confidence score
      - reason: Why this task is suggested
      - relatedContext: What context led to this suggestion

      Return JSON array:
      [
        {
          "title": "Task title",
          "description": "Task description",
          "type": "task",
          "priority": "medium",
          "dueDate": "2024-01-15T10:00:00.000Z",
          "confidence": 0.8,
          "reason": "Reason for suggestion",
          "relatedContext": "Context that led to suggestion"
        }
      ]
      `

      const result = await model.generateContent(suggestionPrompt)
      const response = await result.response
      const text = response.text()

      const jsonMatch = text.match(/\[[\s\S]*\]/)
      if (jsonMatch) {
        const suggestions = JSON.parse(jsonMatch[0])
        return {
          suggestions: suggestions.map((s: any) => ({
            ...s,
            dueDate: s.dueDate ? new Date(s.dueDate) : undefined
          })),
          success: true
        }
      }

      return {
        suggestions: [],
        success: true
      }

    } catch (error) {
      console.error('Error suggesting tasks:', error)
      return {
        suggestions: [],
        success: false,
        error: error instanceof Error ? error.message : 'Failed to suggest tasks'
      }
    }
  }

  async getTaskStatistics(userId: string, days: number = 30): Promise<{
    stats: {
      totalTasks: number
      completedTasks: number
      pendingTasks: number
      overdueTasks: number
      tasksByType: { [type: string]: number }
      tasksByPriority: { [priority: string]: number }
      completionRate: number
      averageCompletionTime: number // in days
    }
    success: boolean
    error?: string
  }> {
    try {
      const dateFrom = new Date()
      dateFrom.setDate(dateFrom.getDate() - days)

      const tasks = await (prisma as any).aITask.findMany({
        where: {
          userId,
          createdAt: { gte: dateFrom }
        }
      })

      const now = new Date()
      const totalTasks = tasks.length
      const completedTasks = tasks.filter(t => t.status === 'completed').length
      const pendingTasks = tasks.filter(t => t.status === 'pending' || t.status === 'in_progress').length
      const overdueTasks = tasks.filter(t => 
        t.dueDate && t.dueDate < now && t.status !== 'completed'
      ).length

      const tasksByType: { [type: string]: number } = {}
      const tasksByPriority: { [priority: string]: number } = {}

      tasks.forEach(task => {
        tasksByType[task.type] = (tasksByType[task.type] || 0) + 1
        tasksByPriority[task.priority] = (tasksByPriority[task.priority] || 0) + 1
      })

      const completionRate = totalTasks > 0 ? (completedTasks / totalTasks) * 100 : 0

      // Calculate average completion time
      const completedTasksWithTime = tasks.filter(t => 
        t.status === 'completed' && t.completedAt && t.createdAt
      )
      
      const averageCompletionTime = completedTasksWithTime.length > 0
        ? completedTasksWithTime.reduce((sum, task) => {
            const completionTime = task.completedAt!.getTime() - task.createdAt.getTime()
            return sum + (completionTime / (1000 * 60 * 60 * 24)) // Convert to days
          }, 0) / completedTasksWithTime.length
        : 0

      return {
        stats: {
          totalTasks,
          completedTasks,
          pendingTasks,
          overdueTasks,
          tasksByType,
          tasksByPriority,
          completionRate,
          averageCompletionTime
        },
        success: true
      }

    } catch (error) {
      console.error('Error getting task statistics:', error)
      return {
        stats: {
          totalTasks: 0,
          completedTasks: 0,
          pendingTasks: 0,
          overdueTasks: 0,
          tasksByType: {},
          tasksByPriority: {},
          completionRate: 0,
          averageCompletionTime: 0
        },
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get task statistics'
      }
    }
  }
}

export const aiAgentTaskService = new AIAgentTaskService()
