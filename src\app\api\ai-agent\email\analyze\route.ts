import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from "next-auth/next"
import { authOptions } from '@/lib/auth'
import { aiAgentEmailService } from '@/lib/ai/aiAgentEmail'

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions) as any
    
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const {
      query,
      dateFrom,
      dateTo,
      category = 'all',
      limit = 50
    } = await request.json()

    // Convert date strings to Date objects if provided
    const parsedDateFrom = dateFrom ? new Date(dateFrom) : undefined
    const parsedDateTo = dateTo ? new Date(dateTo) : undefined

    const analysis = await aiAgentEmailService.analyzeEmails(session.user.id, {
      query,
      dateFrom: parsedDateFrom,
      dateTo: parsedDateTo,
      category,
      limit
    })

    return NextResponse.json({
      analysis,
      success: true
    })

  } catch (error) {
    console.error('Email analysis error:', error)
    return NextResponse.json(
      { error: 'Failed to analyze emails' },
      { status: 500 }
    )
  }
}
