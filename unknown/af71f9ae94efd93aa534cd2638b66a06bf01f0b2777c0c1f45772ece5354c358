/**
 * Preloader Test Component
 * Simple component to test and demonstrate preloading functionality
 */

'use client'

import React, { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useAppPreloader } from '@/hooks/useAppPreloader'
import { useUnifiedCache } from '@/contexts/cache/UnifiedCacheContext'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'

export function PreloaderTest() {
  const { data: session } = useSession()
  const { getCachedData } = useUnifiedCache()
  const [cacheStats, setCacheStats] = useState<any>({})
  const [testResults, setTestResults] = useState<string[]>([])

  const {
    isPreloading,
    preloadSummary,
    startPreload,
    progress
  } = useAppPreloader({
    autoPreload: false, // Manual control for testing
    priority: 'high',
    modules: {
      gmail: true,
      calendar: true,
      meet: true,
      aiAgent: true
    },
    onPreloadComplete: (summary) => {
      setTestResults(prev => [...prev, `✅ Preload completed: ${summary.successCount}/${summary.totalItems} items in ${summary.totalTime}ms`])
      updateCacheStats()
    },
    onPreloadError: (error) => {
      setTestResults(prev => [...prev, `❌ Preload error: ${error.message}`])
    }
  })

  // Update cache statistics
  const updateCacheStats = () => {
    const stats = {
      gmail: {
        inbox: !!getCachedData('gmail', '/api/gmail/inbox'),
        sent: !!getCachedData('gmail', '/api/gmail/sent'),
        drafts: !!getCachedData('gmail', '/api/gmail/drafts'),
        labels: !!getCachedData('gmail', '/api/gmail/labels')
      },
      calendar: {
        events: !!getCachedData('calendar', 'events'),
        colors: !!getCachedData('calendar', 'colors'),
        calendars: !!getCachedData('calendar', 'calendars')
      },
      pages: {
        inbox: !!getCachedData('aiAgent', 'page:/dashboard/mail/inbox'),
        calendar: !!getCachedData('aiAgent', 'page:/dashboard/calendar'),
        meet: !!getCachedData('aiAgent', 'page:/dashboard/meet')
      }
    }
    setCacheStats(stats)
  }

  // Test individual cache operations
  const testCacheOperations = async () => {
    setTestResults(prev => [...prev, '🧪 Testing cache operations...'])
    
    try {
      // Test Gmail inbox cache
      const response = await fetch('/api/gmail/inbox?maxResults=5')
      if (response.ok) {
        const data = await response.json()
        setTestResults(prev => [...prev, `📧 Gmail inbox: ${data.emails?.length || 0} emails loaded`])
      }

      // Test Calendar cache
      const calResponse = await fetch('/api/calendar')
      if (calResponse.ok) {
        const calData = await calResponse.json()
        setTestResults(prev => [...prev, `📅 Calendar: ${calData.events?.length || 0} events loaded`])
      }

      updateCacheStats()
      setTestResults(prev => [...prev, '✅ Cache operations test completed'])
    } catch (error) {
      setTestResults(prev => [...prev, `❌ Cache test error: ${error}`])
    }
  }

  // Clear all test results
  const clearResults = () => {
    setTestResults([])
  }

  // Update cache stats on mount
  useEffect(() => {
    updateCacheStats()
  }, [])

  if (!session?.user) {
    return (
      <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
        <p className="text-yellow-800">Please sign in to test preloader functionality</p>
      </div>
    )
  }

  return (
    <div className="p-6 bg-white border border-gray-200 rounded-lg shadow-sm">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">
        Preloader Test Dashboard
      </h3>

      {/* Control Buttons */}
      <div className="flex flex-wrap gap-2 mb-6">
        <Button
          onClick={startPreload}
          disabled={isPreloading}
          className="bg-blue-600 hover:bg-blue-700"
        >
          {isPreloading ? 'Preloading...' : 'Start Preload Test'}
        </Button>
        
        <Button
          onClick={testCacheOperations}
          variant="outline"
        >
          Test Cache Operations
        </Button>
        
        <Button
          onClick={updateCacheStats}
          variant="outline"
        >
          Refresh Cache Stats
        </Button>
        
        <Button
          onClick={clearResults}
          variant="outline"
        >
          Clear Results
        </Button>
      </div>

      {/* Preload Status */}
      {isPreloading && (
        <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded">
          <div className="flex items-center space-x-2">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
            <span className="text-blue-800 font-medium">Preloading in progress...</span>
          </div>
          <div className="mt-2 text-sm text-blue-600">
            Queue Size: {progress.queueSize}
          </div>
        </div>
      )}

      {/* Preload Summary */}
      {preloadSummary && (
        <div className="mb-4 p-3 bg-green-50 border border-green-200 rounded">
          <h4 className="font-medium text-green-800 mb-2">Last Preload Summary</h4>
          <div className="text-sm text-green-700 space-y-1">
            <div>Success Rate: {preloadSummary.successCount}/{preloadSummary.totalItems} ({((preloadSummary.successCount / preloadSummary.totalItems) * 100).toFixed(1)}%)</div>
            <div>Total Time: {preloadSummary.totalTime}ms</div>
            <div>Cache Hit Rate: {(preloadSummary.cacheHitRate * 100).toFixed(1)}%</div>
            <div>Average Load Time: {preloadSummary.averageLoadTime.toFixed(0)}ms</div>
          </div>
        </div>
      )}

      {/* Cache Statistics */}
      <div className="mb-6">
        <h4 className="font-medium text-gray-900 mb-3">Cache Status</h4>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {/* Gmail Cache */}
          <div className="p-3 bg-gray-50 rounded">
            <h5 className="font-medium text-gray-700 mb-2">Gmail Cache</h5>
            <div className="space-y-1 text-sm">
              <div className="flex justify-between">
                <span>Inbox:</span>
                <Badge variant={cacheStats.gmail?.inbox ? "default" : "secondary"}>
                  {cacheStats.gmail?.inbox ? "Cached" : "Empty"}
                </Badge>
              </div>
              <div className="flex justify-between">
                <span>Sent:</span>
                <Badge variant={cacheStats.gmail?.sent ? "default" : "secondary"}>
                  {cacheStats.gmail?.sent ? "Cached" : "Empty"}
                </Badge>
              </div>
              <div className="flex justify-between">
                <span>Drafts:</span>
                <Badge variant={cacheStats.gmail?.drafts ? "default" : "secondary"}>
                  {cacheStats.gmail?.drafts ? "Cached" : "Empty"}
                </Badge>
              </div>
              <div className="flex justify-between">
                <span>Labels:</span>
                <Badge variant={cacheStats.gmail?.labels ? "default" : "secondary"}>
                  {cacheStats.gmail?.labels ? "Cached" : "Empty"}
                </Badge>
              </div>
            </div>
          </div>

          {/* Calendar Cache */}
          <div className="p-3 bg-gray-50 rounded">
            <h5 className="font-medium text-gray-700 mb-2">Calendar Cache</h5>
            <div className="space-y-1 text-sm">
              <div className="flex justify-between">
                <span>Events:</span>
                <Badge variant={cacheStats.calendar?.events ? "default" : "secondary"}>
                  {cacheStats.calendar?.events ? "Cached" : "Empty"}
                </Badge>
              </div>
              <div className="flex justify-between">
                <span>Colors:</span>
                <Badge variant={cacheStats.calendar?.colors ? "default" : "secondary"}>
                  {cacheStats.calendar?.colors ? "Cached" : "Empty"}
                </Badge>
              </div>
              <div className="flex justify-between">
                <span>Calendars:</span>
                <Badge variant={cacheStats.calendar?.calendars ? "default" : "secondary"}>
                  {cacheStats.calendar?.calendars ? "Cached" : "Empty"}
                </Badge>
              </div>
            </div>
          </div>

          {/* Page Cache */}
          <div className="p-3 bg-gray-50 rounded">
            <h5 className="font-medium text-gray-700 mb-2">Page Cache</h5>
            <div className="space-y-1 text-sm">
              <div className="flex justify-between">
                <span>Inbox Page:</span>
                <Badge variant={cacheStats.pages?.inbox ? "default" : "secondary"}>
                  {cacheStats.pages?.inbox ? "Cached" : "Empty"}
                </Badge>
              </div>
              <div className="flex justify-between">
                <span>Calendar Page:</span>
                <Badge variant={cacheStats.pages?.calendar ? "default" : "secondary"}>
                  {cacheStats.pages?.calendar ? "Cached" : "Empty"}
                </Badge>
              </div>
              <div className="flex justify-between">
                <span>Meet Page:</span>
                <Badge variant={cacheStats.pages?.meet ? "default" : "secondary"}>
                  {cacheStats.pages?.meet ? "Cached" : "Empty"}
                </Badge>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Test Results */}
      {testResults.length > 0 && (
        <div>
          <h4 className="font-medium text-gray-900 mb-3">Test Results</h4>
          <div className="bg-gray-50 rounded p-3 max-h-64 overflow-y-auto">
            {testResults.map((result, index) => (
              <div key={index} className="text-sm text-gray-700 py-1 font-mono">
                {result}
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}
