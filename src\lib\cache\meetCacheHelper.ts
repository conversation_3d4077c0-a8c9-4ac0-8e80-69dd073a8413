// Meet Cache Helper
// Provides Meet-specific caching functionality using the unified cache system

import { cacheService } from '@/contexts/cache'
import { CachedMeetingSpace, CachedConferenceRecord, CachedParticipant } from '@/contexts/cache/types'

export class MeetCacheHelper {
  private static readonly CACHE_DURATION = 15 * 60 * 1000 // 15 minutes for Meet data

  /**
   * Cache a meeting space
   */
  static cacheMeetingSpace(spaceId: string, spaceData: any): void {
    const cachedSpace: CachedMeetingSpace = {
      name: spaceData.name,
      meetingUri: spaceData.meetingUri,
      meetingCode: spaceData.meetingCode,
      config: spaceData.config,
      activeConference: spaceData.activeConference
    }

    cacheService.set('meet', `spaces.${spaceId}`, cachedSpace, this.CACHE_DURATION)
    console.log(`🎥 Cached meeting space: ${spaceId}`)
  }

  /**
   * Get cached meeting space
   */
  static getCachedMeetingSpace(spaceId: string): CachedMeetingSpace | null {
    const cached = cacheService.get<CachedMeetingSpace>('meet', `spaces.${spaceId}`)
    if (cached) {
      console.log(`🎥 Retrieved cached meeting space: ${spaceId}`)
    }
    return cached
  }

  /**
   * Cache a conference record
   */
  static cacheConferenceRecord(conferenceId: string, conferenceData: any): void {
    const cachedConference: CachedConferenceRecord = {
      name: conferenceData.name,
      startTime: conferenceData.startTime,
      endTime: conferenceData.endTime,
      expireTime: conferenceData.expireTime,
      space: conferenceData.space
    }

    cacheService.set('meet', `conferences.${conferenceId}`, cachedConference, this.CACHE_DURATION)
    console.log(`🎥 Cached conference record: ${conferenceId}`)
  }

  /**
   * Get cached conference record
   */
  static getCachedConferenceRecord(conferenceId: string): CachedConferenceRecord | null {
    const cached = cacheService.get<CachedConferenceRecord>('meet', `conferences.${conferenceId}`)
    if (cached) {
      console.log(`🎥 Retrieved cached conference record: ${conferenceId}`)
    }
    return cached
  }

  /**
   * Cache participants for a conference
   */
  static cacheParticipants(conferenceId: string, participants: any[]): void {
    const cachedParticipants: CachedParticipant[] = participants.map(participant => ({
      signedinUser: participant.signedinUser,
      anonymousUser: participant.anonymousUser,
      phoneUser: participant.phoneUser,
      earliestStartTime: participant.earliestStartTime,
      latestEndTime: participant.latestEndTime
    }))

    cacheService.set('meet', `participants.${conferenceId}`, cachedParticipants, this.CACHE_DURATION)
    console.log(`🎥 Cached ${participants.length} participants for conference: ${conferenceId}`)
  }

  /**
   * Get cached participants
   */
  static getCachedParticipants(conferenceId: string): CachedParticipant[] | null {
    const cached = cacheService.get<CachedParticipant[]>('meet', `participants.${conferenceId}`)
    if (cached) {
      console.log(`🎥 Retrieved ${cached.length} cached participants for conference: ${conferenceId}`)
    }
    return cached
  }

  /**
   * Cache meeting analytics data
   */
  static cacheMeetingAnalytics(key: string, analyticsData: any): void {
    cacheService.set('meet', `analytics.${key}`, analyticsData, this.CACHE_DURATION)
    console.log(`🎥 Cached meeting analytics for key: ${key}`)
  }

  /**
   * Get cached meeting analytics
   */
  static getCachedMeetingAnalytics(key: string): any | null {
    const cached = cacheService.get('meet', `analytics.${key}`)
    if (cached) {
      console.log(`🎥 Retrieved cached meeting analytics for key: ${key}`)
    }
    return cached
  }

  /**
   * Cache active meeting spaces for a user
   */
  static cacheActiveMeetingSpaces(userId: string, spaces: any[]): void {
    const cacheKey = `active_spaces_${userId}`
    const spacesData = {
      spaces: spaces.map(space => ({
        name: space.name,
        meetingUri: space.meetingUri,
        meetingCode: space.meetingCode,
        config: space.config,
        activeConference: space.activeConference
      })),
      timestamp: Date.now(),
      userId
    }

    cacheService.set('meet', cacheKey, spacesData, this.CACHE_DURATION)
    console.log(`🎥 Cached ${spaces.length} active meeting spaces for user: ${userId}`)
  }

  /**
   * Get cached active meeting spaces
   */
  static getCachedActiveMeetingSpaces(userId: string): any | null {
    const cacheKey = `active_spaces_${userId}`
    const cached = cacheService.get('meet', cacheKey)
    if (cached) {
      console.log(`🎥 Retrieved cached active meeting spaces for user: ${userId}`)
    }
    return cached
  }

  /**
   * Cache recent conferences for a user
   */
  static cacheRecentConferences(userId: string, conferences: any[]): void {
    const cacheKey = `recent_conferences_${userId}`
    const conferencesData = {
      conferences: conferences.map(conf => ({
        name: conf.name,
        startTime: conf.startTime,
        endTime: conf.endTime,
        expireTime: conf.expireTime,
        space: conf.space
      })),
      timestamp: Date.now(),
      userId
    }

    cacheService.set('meet', cacheKey, conferencesData, this.CACHE_DURATION)
    console.log(`🎥 Cached ${conferences.length} recent conferences for user: ${userId}`)
  }

  /**
   * Get cached recent conferences
   */
  static getCachedRecentConferences(userId: string): any | null {
    const cacheKey = `recent_conferences_${userId}`
    const cached = cacheService.get('meet', cacheKey)
    if (cached) {
      console.log(`🎥 Retrieved cached recent conferences for user: ${userId}`)
    }
    return cached
  }

  /**
   * Invalidate Meet cache for specific patterns
   */
  static invalidateMeetCache(pattern?: string): number {
    if (pattern) {
      return cacheService.invalidate({
        namespace: 'meet',
        pattern
      })
    } else {
      cacheService.clear('meet')
      return 0
    }
  }

  /**
   * Preload common Meet data for a user
   */
  static async preloadMeetData(userId: string): Promise<void> {
    try {
      console.log('🎥 Starting Meet data preloading...')

      // Meet API endpoints removed - functionality disabled
      // const spacesResponse = await fetch(`/api/meet/spaces?userId=${userId}`)
      // if (spacesResponse.ok) {
      //   const spacesData = await spacesResponse.json()
      //   if (spacesData.spaces) {
      //     this.cacheActiveMeetingSpaces(userId, spacesData.spaces)
      //   }
      // }

      // const conferencesResponse = await fetch(`/api/meet/conferences/recent?userId=${userId}&limit=10`)
      // if (conferencesResponse.ok) {
      //   const conferencesData = await conferencesResponse.json()
      //   if (conferencesData.conferences) {
      //     this.cacheRecentConferences(userId, conferencesData.conferences)
      //   }
      // }

      console.log('✅ Meet data preloading completed')
    } catch (error) {
      console.error('❌ Error preloading Meet data:', error)
    }
  }

  /**
   * Get Meet cache statistics
   */
  static getMeetCacheStats(): {
    totalSpaces: number
    totalConferences: number
    totalParticipants: number
    cacheKeys: string[]
  } {
    const stats = cacheService.getStats()
    const namespaceSize = cacheService.getNamespaceSize('meet')
    
    // This is a simplified version - in a real implementation,
    // you'd traverse the meet namespace to get detailed stats
    return {
      totalSpaces: 0,
      totalConferences: 0,
      totalParticipants: 0,
      cacheKeys: []
    }
  }

  /**
   * Get cache keys for different Meet data types
   */
  static getCacheKeys(userId: string) {
    return {
      ACTIVE_SPACES: `active_spaces_${userId}`,
      RECENT_CONFERENCES: `recent_conferences_${userId}`,
      USER_ANALYTICS: `analytics_user_${userId}`,
      DASHBOARD_DATA: `dashboard_${userId}`
    }
  }
}

// Export utility functions for easy use
export const MeetCache = {
  // Space caching
  cacheSpace: MeetCacheHelper.cacheMeetingSpace.bind(MeetCacheHelper),
  getSpace: MeetCacheHelper.getCachedMeetingSpace.bind(MeetCacheHelper),
  
  // Conference caching
  cacheConference: MeetCacheHelper.cacheConferenceRecord.bind(MeetCacheHelper),
  getConference: MeetCacheHelper.getCachedConferenceRecord.bind(MeetCacheHelper),
  
  // Participant caching
  cacheParticipants: MeetCacheHelper.cacheParticipants.bind(MeetCacheHelper),
  getParticipants: MeetCacheHelper.getCachedParticipants.bind(MeetCacheHelper),
  
  // Analytics caching
  cacheAnalytics: MeetCacheHelper.cacheMeetingAnalytics.bind(MeetCacheHelper),
  getAnalytics: MeetCacheHelper.getCachedMeetingAnalytics.bind(MeetCacheHelper),
  
  // User-specific caching
  cacheActiveSpaces: MeetCacheHelper.cacheActiveMeetingSpaces.bind(MeetCacheHelper),
  getActiveSpaces: MeetCacheHelper.getCachedActiveMeetingSpaces.bind(MeetCacheHelper),
  cacheRecentConferences: MeetCacheHelper.cacheRecentConferences.bind(MeetCacheHelper),
  getRecentConferences: MeetCacheHelper.getCachedRecentConferences.bind(MeetCacheHelper),
  
  // Cache management
  invalidate: MeetCacheHelper.invalidateMeetCache.bind(MeetCacheHelper),
  preload: MeetCacheHelper.preloadMeetData.bind(MeetCacheHelper),
  getStats: MeetCacheHelper.getMeetCacheStats.bind(MeetCacheHelper),
  
  // Utility
  getCacheKeys: MeetCacheHelper.getCacheKeys.bind(MeetCacheHelper)
}
