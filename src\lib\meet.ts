/**
 * Google Meet API Integration
 * Provides functions for interacting with Google Meet API
 */

import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { getGoogleClient } from "@/lib/unified-google-client"

// Meet API types
export interface MeetConference {
  name: string
  conferenceId: string
  createTime: string
  endTime?: string
  expireTime: string
  participantCount?: number
  maxParticipantCount?: number
}

export interface MeetSpace {
  name: string
  meetingUri: string
  meetingCode: string
  config?: {
    entryPointAccess?: string
    accessType?: string
  }
  activeConference?: MeetConference
}

export interface MeetParticipant {
  name: string
  anonymousUser?: {
    displayName: string
  }
  signedinUser?: {
    user: string
    displayName: string
  }
  phoneUser?: {
    displayName: string
  }
  earliestArrivalTime?: string
  latestDepartureTime?: string
}

export interface MeetConferenceRecord {
  name: string
  startTime: string
  endTime: string
  space: string
  state: 'STARTED' | 'ENDED' | 'FILE_GENERATED'
  driveDestination?: {
    file: string
    exportUri: string
  }
}

/**
 * Get Google Meet client
 */
export async function getMeetClient() {
  const session = await getServerSession(authOptions)
  if (!session?.user?.email) {
    throw new Error('No authenticated user found')
  }

  const googleClient = await getGoogleClient(session.user.email)
  return googleClient
}

/**
 * Create a new Meet space
 */
export async function createMeetSpace(): Promise<MeetSpace> {
  const client = await getMeetClient()
  
  // Note: This is a placeholder implementation
  // The actual Google Meet API for creating spaces may have different endpoints
  const response = await client.request({
    url: 'https://meet.googleapis.com/v2/spaces',
    method: 'POST',
    data: {}
  })

  return response.data
}

/**
 * Get Meet space details
 */
export async function getMeetSpace(spaceName: string): Promise<MeetSpace> {
  const client = await getMeetClient()
  
  const response = await client.request({
    url: `https://meet.googleapis.com/v2/${spaceName}`,
    method: 'GET'
  })

  return response.data
}

/**
 * List conference records
 */
export async function listConferenceRecords(
  spaceName?: string,
  pageSize: number = 50,
  pageToken?: string
): Promise<{
  conferenceRecords: MeetConferenceRecord[]
  nextPageToken?: string
}> {
  const client = await getMeetClient()
  
  const params = new URLSearchParams({
    pageSize: pageSize.toString()
  })
  
  if (pageToken) {
    params.append('pageToken', pageToken)
  }

  const url = spaceName 
    ? `https://meet.googleapis.com/v2/${spaceName}/conferenceRecords`
    : `https://meet.googleapis.com/v2/conferenceRecords`

  const response = await client.request({
    url: `${url}?${params.toString()}`,
    method: 'GET'
  })

  return {
    conferenceRecords: response.data.conferenceRecords || [],
    nextPageToken: response.data.nextPageToken
  }
}

/**
 * Get conference record details
 */
export async function getConferenceRecord(recordName: string): Promise<MeetConferenceRecord> {
  const client = await getMeetClient()
  
  const response = await client.request({
    url: `https://meet.googleapis.com/v2/${recordName}`,
    method: 'GET'
  })

  return response.data
}

/**
 * List participants for a conference
 */
export async function listParticipants(
  conferenceRecordName: string,
  pageSize: number = 50,
  pageToken?: string
): Promise<{
  participants: MeetParticipant[]
  nextPageToken?: string
  totalSize?: number
}> {
  const client = await getMeetClient()
  
  const params = new URLSearchParams({
    pageSize: pageSize.toString()
  })
  
  if (pageToken) {
    params.append('pageToken', pageToken)
  }

  const response = await client.request({
    url: `https://meet.googleapis.com/v2/${conferenceRecordName}/participants?${params.toString()}`,
    method: 'GET'
  })

  return {
    participants: response.data.participants || [],
    nextPageToken: response.data.nextPageToken,
    totalSize: response.data.totalSize
  }
}

/**
 * Format Meet API response for consistent error handling
 */
export function formatMeetApiResponse(
  data: any,
  message: string = "Operation completed successfully",
  success: boolean = true
) {
  return {
    success,
    message,
    data,
    timestamp: new Date().toISOString()
  }
}

/**
 * Handle Meet API errors
 */
export function handleMeetApiError(error: any) {
  console.error('Meet API Error:', error)
  
  const errorMessage = error.response?.data?.error?.message || 
                      error.message || 
                      'An unknown error occurred'
  
  return {
    success: false,
    error: errorMessage,
    timestamp: new Date().toISOString()
  }
}

/**
 * Check if user has Meet API access
 */
export async function checkMeetAccess(): Promise<boolean> {
  try {
    const client = await getMeetClient()
    // Try to make a simple API call to check access
    await client.request({
      url: 'https://meet.googleapis.com/v2/conferenceRecords?pageSize=1',
      method: 'GET'
    })
    return true
  } catch (error) {
    console.error('Meet access check failed:', error)
    return false
  }
}

/**
 * Get Meet statistics for a user
 */
export async function getMeetStats(): Promise<{
  totalConferences: number
  recentConferences: number
  totalParticipants: number
}> {
  try {
    const { conferenceRecords } = await listConferenceRecords(undefined, 100)
    
    const now = new Date()
    const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
    
    const recentConferences = conferenceRecords.filter(record => 
      new Date(record.startTime) >= thirtyDaysAgo
    )

    // Get participant count for recent conferences
    let totalParticipants = 0
    for (const record of recentConferences.slice(0, 10)) { // Limit to avoid rate limits
      try {
        const { totalSize } = await listParticipants(record.name, 1)
        totalParticipants += totalSize || 0
      } catch (error) {
        console.warn('Failed to get participants for conference:', record.name)
      }
    }

    return {
      totalConferences: conferenceRecords.length,
      recentConferences: recentConferences.length,
      totalParticipants
    }
  } catch (error) {
    console.error('Failed to get Meet stats:', error)
    return {
      totalConferences: 0,
      recentConferences: 0,
      totalParticipants: 0
    }
  }
}
