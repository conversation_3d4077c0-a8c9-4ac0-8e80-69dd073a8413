/**
 * Unified Cache Context
 * React Context for centralized cache management across all application modules
 */

'use client'

import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react'
import {
  cacheManager,
  CacheStats,
  ModuleCacheConfig,
  CacheOperationResult,
  preloadAllUserData,
  invalidateAllUser<PERSON>ache,
  getAllCacheStats,
  clearAllCache,
  initializeUnifiedCache
} from '@/lib/cache/unified'

interface UnifiedCacheContextType {
  // Cache statistics
  stats: CacheStats | null
  isLoading: boolean
  
  // Cache operations
  preloadUserData: (userId: string, options?: {
    gmail?: boolean
    calendar?: boolean
    meet?: boolean
    aiAgent?: boolean
  }) => Promise<any>

  invalidateUserCache: (userId: string) => {
    gmail: number
    calendar: number
    meet: number
    aiAgent: number
  }
  
  clearCache: () => void
  refreshStats: () => void
  
  // Configuration
  updateConfig: (config: Partial<ModuleCacheConfig>) => void
  
  // Module-specific helpers
  gmail: {
    invalidateEmails: (userId: string, query?: string) => number
    preloadInbox: (userId: string) => Promise<CacheOperationResult<any>>
  }
  
  calendar: {
    invalidateEvents: (userId: string, calendarId?: string) => number
    preloadToday: (userId: string) => Promise<CacheOperationResult<any>>
  }

  meet: {
    invalidateSpaces: (userId: string, spaceId?: string) => number
    preloadActive: (userId: string) => Promise<CacheOperationResult<any>>
  }

  aiAgent: {
    invalidateConversations: (userId: string) => number
    preloadRecent: (userId: string) => Promise<CacheOperationResult<any>>
  }
}

const UnifiedCacheContext = createContext<UnifiedCacheContextType | undefined>(undefined)

interface UnifiedCacheProviderProps {
  children: ReactNode
  config?: Partial<ModuleCacheConfig>
  autoPreload?: boolean
  userId?: string
}

export function UnifiedCacheProvider({
  children,
  config,
  autoPreload = true,
  userId
}: UnifiedCacheProviderProps) {
  const [stats, setStats] = useState<CacheStats | null>(null)
  const [isLoading, setIsLoading] = useState(false)

  // Initialize cache system
  useEffect(() => {
    initializeUnifiedCache(config)
    refreshStats()
  }, [config])

  // Auto-preload user data if enabled and userId is provided
  useEffect(() => {
    if (autoPreload && userId) {
      preloadUserData(userId)
    }
  }, [autoPreload, userId])

  // Refresh stats periodically
  useEffect(() => {
    const interval = setInterval(refreshStats, 30000) // Every 30 seconds
    return () => clearInterval(interval)
  }, [])

  const refreshStats = () => {
    try {
      const currentStats = getAllCacheStats()
      setStats(currentStats)
    } catch (error) {
      console.error('Failed to refresh cache stats:', error)
    }
  }

  const preloadUserData = async (
    targetUserId: string,
    options?: {
      gmail?: boolean
      calendar?: boolean
      aiAgent?: boolean
    }
  ) => {
    setIsLoading(true)
    try {
      const results = await preloadAllUserData(targetUserId, options)
      refreshStats()
      return results
    } catch (error) {
      console.error('Failed to preload user data:', error)
      throw error
    } finally {
      setIsLoading(false)
    }
  }

  const invalidateUserCache = (targetUserId: string) => {
    const results = invalidateAllUserCache(targetUserId)
    refreshStats()
    return results
  }

  const clearCache = () => {
    clearAllCache()
    refreshStats()
  }

  const updateConfig = (newConfig: Partial<ModuleCacheConfig>) => {
    cacheManager.updateConfig(newConfig)
    refreshStats()
  }

  // Module-specific helpers
  const gmailHelpers = {
    invalidateEmails: (targetUserId: string, query?: string) => {
      const { invalidateEmailCache } = require('@/lib/cache/unified/gmailCache')
      const result = invalidateEmailCache(targetUserId, query)
      refreshStats()
      return result
    },
    
    preloadInbox: async (targetUserId: string) => {
      const { preloadGmailData } = await import('@/lib/cache/unified/gmailCache')
      const result = await preloadGmailData(targetUserId, { loadInbox: true })
      refreshStats()
      return result
    }
  }

  const calendarHelpers = {
    invalidateEvents: (targetUserId: string, calendarId?: string) => {
      const { invalidateCalendarCache } = require('@/lib/cache/unified/calendarCache')
      const result = invalidateCalendarCache(targetUserId, calendarId)
      refreshStats()
      return result
    },

    preloadToday: async (targetUserId: string) => {
      const { preloadCalendarData } = await import('@/lib/cache/unified/calendarCache')
      const result = await preloadCalendarData(targetUserId, { loadToday: true })
      refreshStats()
      return result
    }
  }

  const meetHelpers = {
    invalidateSpaces: (targetUserId: string, spaceId?: string) => {
      const { invalidateMeetSpaceCache } = require('@/lib/cache/unified/meetCache')
      const result = invalidateMeetSpaceCache(targetUserId, spaceId)
      refreshStats()
      return result
    },

    preloadActive: async (targetUserId: string) => {
      const { preloadMeetData } = await import('@/lib/cache/unified/meetCache')
      const result = await preloadMeetData(targetUserId, { loadActiveSpaces: true })
      refreshStats()
      return result
    }
  }

  const aiAgentHelpers = {
    invalidateConversations: (targetUserId: string) => {
      const { invalidateConversationCache } = require('@/lib/cache/unified/aiAgentCache')
      const result = invalidateConversationCache(targetUserId)
      refreshStats()
      return result
    },

    preloadRecent: async (targetUserId: string) => {
      const { preloadAiAgentData } = await import('@/lib/cache/unified/aiAgentCache')
      const result = await preloadAiAgentData(targetUserId, { loadRecentConversations: true })
      refreshStats()
      return result
    }
  }

  const contextValue: UnifiedCacheContextType = {
    stats,
    isLoading,
    preloadUserData,
    invalidateUserCache,
    clearCache,
    refreshStats,
    updateConfig,
    gmail: gmailHelpers,
    calendar: calendarHelpers,
    meet: meetHelpers,
    aiAgent: aiAgentHelpers
  }

  return (
    <UnifiedCacheContext.Provider value={contextValue}>
      {children}
    </UnifiedCacheContext.Provider>
  )
}

/**
 * Hook to use the unified cache context
 */
export function useUnifiedCache(): UnifiedCacheContextType {
  const context = useContext(UnifiedCacheContext)
  if (context === undefined) {
    throw new Error('useUnifiedCache must be used within a UnifiedCacheProvider')
  }
  return context
}

/**
 * Hook for Gmail-specific cache operations
 */
export function useGmailCache(userId?: string) {
  const { gmail, stats, refreshStats } = useUnifiedCache()
  
  return {
    ...gmail,
    stats: stats?.moduleStats.gmail,
    refreshStats,
    userId
  }
}

/**
 * Hook for Calendar-specific cache operations
 */
export function useCalendarCache(userId?: string) {
  const { calendar, stats, refreshStats } = useUnifiedCache()

  return {
    ...calendar,
    stats: stats?.moduleStats.calendar,
    refreshStats,
    userId
  }
}

/**
 * Hook for Meet-specific cache operations
 */
export function useMeetCache(userId?: string) {
  const { meet, stats, refreshStats } = useUnifiedCache()

  return {
    ...meet,
    stats: stats?.moduleStats.meet,
    refreshStats,
    userId
  }
}

/**
 * Hook for AI Agent-specific cache operations
 */
export function useAiAgentCache(userId?: string) {
  const { aiAgent, stats, refreshStats } = useUnifiedCache()

  return {
    ...aiAgent,
    stats: stats?.moduleStats.aiAgent,
    refreshStats,
    userId
  }
}

/**
 * Hook for cache statistics monitoring
 */
export function useCacheStats() {
  const { stats, refreshStats, isLoading } = useUnifiedCache()
  
  return {
    stats,
    refreshStats,
    isLoading,
    hitRate: stats?.hitRate || 0,
    missRate: stats?.missRate || 0,
    totalEntries: stats?.totalEntries || 0,
    memoryUsage: stats?.memoryUsage || 0
  }
}
