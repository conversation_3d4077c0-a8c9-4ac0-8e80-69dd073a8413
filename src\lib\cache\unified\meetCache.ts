/**
 * Meet Cache Helper - Unified Cache System
 * Meet-specific caching functions using the unified cache manager
 */

import { cacheManager } from './cacheManager'
import {
  CachedMeetSpace,
  CachedMeetConference,
  CacheOperationResult
} from './types'

const MODULE_NAME = 'meet'

/**
 * Meet Space Caching
 */
export function getCachedMeetSpace(
  userId: string,
  spaceId: string
): CacheOperationResult<CachedMeetSpace> {
  const key = `space:${spaceId}`
  return cacheManager.get<CachedMeetSpace>(MODULE_NAME, key, userId)
}

export function setCachedMeetSpace(
  userId: string,
  space: CachedMeetSpace
): CacheOperationResult<CachedMeetSpace> {
  const key = `space:${space.id}`
  return cacheManager.set(MODULE_NAME, key, space, userId)
}

export function updateCachedMeetSpace(
  userId: string,
  spaceId: string,
  updates: Partial<CachedMeetSpace>
): CacheOperationResult<CachedMeetSpace> {
  const existing = getCachedMeetSpace(userId, spaceId)
  if (existing.success && existing.data) {
    const updatedSpace = { ...existing.data, ...updates }
    return setCachedMeetSpace(userId, updatedSpace)
  }
  return {
    success: false,
    error: 'Meet space not found in cache',
    fromCache: false,
    timestamp: Date.now()
  }
}

/**
 * Meet Space List Caching
 */
export function getCachedMeetSpaceList(
  userId: string,
  filter?: string
): CacheOperationResult<CachedMeetSpace[]> {
  const key = `spaces:list:${filter || 'all'}`
  return cacheManager.get<CachedMeetSpace[]>(MODULE_NAME, key, userId)
}

export function setCachedMeetSpaceList(
  userId: string,
  spaces: CachedMeetSpace[],
  filter?: string
): CacheOperationResult<CachedMeetSpace[]> {
  const key = `spaces:list:${filter || 'all'}`
  return cacheManager.set(MODULE_NAME, key, spaces, userId)
}

/**
 * Meet Conference Caching
 */
export function getCachedMeetConference(
  userId: string,
  conferenceId: string
): CacheOperationResult<CachedMeetConference> {
  const key = `conference:${conferenceId}`
  return cacheManager.get<CachedMeetConference>(MODULE_NAME, key, userId)
}

export function setCachedMeetConference(
  userId: string,
  conference: CachedMeetConference
): CacheOperationResult<CachedMeetConference> {
  const key = `conference:${conference.id}`
  return cacheManager.set(MODULE_NAME, key, conference, userId)
}

export function updateCachedMeetConference(
  userId: string,
  conferenceId: string,
  updates: Partial<CachedMeetConference>
): CacheOperationResult<CachedMeetConference> {
  const existing = getCachedMeetConference(userId, conferenceId)
  if (existing.success && existing.data) {
    const updatedConference = { ...existing.data, ...updates }
    return setCachedMeetConference(userId, updatedConference)
  }
  return {
    success: false,
    error: 'Meet conference not found in cache',
    fromCache: false,
    timestamp: Date.now()
  }
}

/**
 * Meet Conference List Caching
 */
export function getCachedMeetConferenceList(
  userId: string,
  status?: string
): CacheOperationResult<CachedMeetConference[]> {
  const key = `conferences:list:${status || 'all'}`
  return cacheManager.get<CachedMeetConference[]>(MODULE_NAME, key, userId)
}

export function setCachedMeetConferenceList(
  userId: string,
  conferences: CachedMeetConference[],
  status?: string
): CacheOperationResult<CachedMeetConference[]> {
  const key = `conferences:list:${status || 'all'}`
  return cacheManager.set(MODULE_NAME, key, conferences, userId)
}

/**
 * Meet Participants Caching
 */
export function getCachedMeetParticipants(
  userId: string,
  meetingId: string
): CacheOperationResult<any[]> {
  const key = `participants:${meetingId}`
  return cacheManager.get<any[]>(MODULE_NAME, key, userId)
}

export function setCachedMeetParticipants(
  userId: string,
  participants: any[],
  meetingId: string
): CacheOperationResult<any[]> {
  const key = `participants:${meetingId}`
  return cacheManager.set(MODULE_NAME, key, participants, userId)
}

/**
 * Cache Invalidation
 */
export function invalidateMeetSpaceCache(
  userId: string,
  spaceId?: string
): number {
  const pattern = spaceId ? `space:${spaceId}` : 'space'
  return cacheManager.invalidate({
    module: MODULE_NAME,
    userId,
    pattern
  })
}

export function invalidateMeetConferenceCache(
  userId: string,
  conferenceId?: string
): number {
  const pattern = conferenceId ? `conference:${conferenceId}` : 'conference'
  return cacheManager.invalidate({
    module: MODULE_NAME,
    userId,
    pattern
  })
}

export function invalidateMeetParticipants(
  userId: string,
  meetingId: string
): boolean {
  return cacheManager.delete(MODULE_NAME, `participants:${meetingId}`, userId)
}

/**
 * Bulk Operations
 */
export function cacheMultipleMeetSpaces(
  userId: string,
  spaces: CachedMeetSpace[]
): CacheOperationResult<CachedMeetSpace[]> {
  const results: CacheOperationResult<CachedMeetSpace>[] = []
  
  spaces.forEach(space => {
    const result = setCachedMeetSpace(userId, space)
    results.push(result)
  })

  const allSuccessful = results.every(r => r.success)
  
  return {
    success: allSuccessful,
    data: allSuccessful ? spaces : undefined,
    error: allSuccessful ? undefined : 'Some spaces failed to cache',
    fromCache: false,
    timestamp: Date.now()
  }
}

export function getMultipleCachedMeetSpaces(
  userId: string,
  spaceIds: string[]
): CacheOperationResult<CachedMeetSpace[]> {
  const spaces: CachedMeetSpace[] = []
  const notFound: string[] = []

  spaceIds.forEach(spaceId => {
    const result = getCachedMeetSpace(userId, spaceId)
    if (result.success && result.data) {
      spaces.push(result.data)
    } else {
      notFound.push(spaceId)
    }
  })

  return {
    success: notFound.length === 0,
    data: spaces,
    error: notFound.length > 0 ? `Spaces not found: ${notFound.join(', ')}` : undefined,
    fromCache: true,
    timestamp: Date.now()
  }
}

/**
 * Preloading
 */
export async function preloadMeetData(
  userId: string,
  options: {
    loadActiveSpaces?: boolean
    loadUpcomingConferences?: boolean
    loadRecentConferences?: boolean
  } = {}
): Promise<CacheOperationResult<any>> {
  try {
    const preloadTasks: Promise<any>[] = []

    if (options.loadActiveSpaces !== false) {
      console.log(`Preloading active Meet spaces for user ${userId}`)
    }

    if (options.loadUpcomingConferences) {
      console.log(`Preloading upcoming conferences for user ${userId}`)
    }

    if (options.loadRecentConferences) {
      console.log(`Preloading recent conferences for user ${userId}`)
    }

    await Promise.all(preloadTasks)

    return {
      success: true,
      data: { preloaded: true },
      fromCache: false,
      timestamp: Date.now()
    }
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Preload failed',
      fromCache: false,
      timestamp: Date.now()
    }
  }
}

/**
 * Statistics and Cleanup
 */
export function getMeetCacheStats() {
  const globalStats = cacheManager.getStats()
  return {
    ...globalStats.moduleStats.meet,
    hitRate: globalStats.hitRate,
    missRate: globalStats.missRate,
    totalEntries: globalStats.totalEntries,
    memoryUsage: globalStats.memoryUsage
  }
}

export function cleanupMeetCache(userId?: string): number {
  return cacheManager.invalidate({
    module: MODULE_NAME,
    userId,
    force: false // Only cleanup expired entries
  })
}

/**
 * Constants
 */
export const MEET_CACHE_KEYS = {
  SPACES: 'spaces',
  SPACE: 'space',
  CONFERENCES: 'conferences',
  CONFERENCE: 'conference',
  PARTICIPANTS: 'participants'
} as const

export const MEET_CACHE_TTL = 10 * 60 * 1000 // 10 minutes

/**
 * Legacy compatibility functions
 */
export function getCachedMeetData(userId: string, key: string) {
  return cacheManager.get(MODULE_NAME, key, userId)
}

export function setCachedMeetData(userId: string, key: string, data: any) {
  return cacheManager.set(MODULE_NAME, key, data, userId)
}

export function invalidateMeetData(userId: string, pattern?: string) {
  return cacheManager.invalidate({
    module: MODULE_NAME,
    userId,
    pattern
  })
}
